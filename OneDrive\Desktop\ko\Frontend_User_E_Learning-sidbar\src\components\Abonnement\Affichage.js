import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Badge, Spinner } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import axiosInstance from "../../services/axiosService";
import abonnementTypeService from "../../services/abonnementTypeService";

const Affichage = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [darkMode, setDarkMode] = useState(false);

  const [abonnements, setAbonnements] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedAbonnement, setSelectedAbonnement] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editedNom, setEditedNom] = useState("");
  const [editedDescription, setEditedDescription] = useState("");
  const [editedPrix, setEditedPrix] = useState("");
  const [editedDuree, setEditedDuree] = useState("");
  const [imageUrls, setImageUrls] = useState({});
  const [selectedImageFile, setSelectedImageFile] = useState(null);
  const [subscriptionTypes, setSubscriptionTypes] = useState([]);
  const [loadingTypes, setLoadingTypes] = useState(true);
  const [typeError, setTypeError] = useState(null);
  const [editedTypeId, setEditedTypeId] = useState("");


  const abonnementsParPage = 3;

  useEffect(() => {
    const fetchAbonnements = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await axiosInstance.get(
          `/api/abonnements/page?page=${
            currentPage - 1
          }&size=${abonnementsParPage}`
        );
        setAbonnements(response.data.content || []);
        setTotalPages(response.data.totalPages || 1);
      } catch (error) {
        console.error("Erreur lors du chargement des abonnements:", error);
        setError(
          error.response?.data?.message ||
            "Erreur lors du chargement des abonnements"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchAbonnements();
  }, [currentPage]);

  const handleDelete = async () => {
    if (!selectedAbonnement || !selectedAbonnement.id) {
      console.error("ID d'abonnement invalide");
      return;
    }

    try {
      // Log de l'ID pour vérifier qu'il est correct
      console.log("ID de l'abonnement à supprimer:", selectedAbonnement.id);

      // Suppression de l'abonnement via l'API
      await axiosInstance.delete(
        `/api/abonnements/delete/${selectedAbonnement.id}`
      );

      // Mise à jour de l'état pour supprimer l'abonnement de la liste affichée
      setAbonnements((prevAbonnements) =>
        prevAbonnements.filter((a) => a.id !== selectedAbonnement.id)
      );

      // Réinitialisation de l'abonnement sélectionné et fermeture du modal
      setSelectedAbonnement(null);
      setShowDeleteModal(false);
      // Dans handleEdit ou handleDelete après succès
      setToastMessage("Abonnement supprimé avec succès !");
      setShowToast(true);
      setTimeout(() => setShowToast(false), 4000);
    } catch (error) {
      // Gestion d'erreur améliorée
      if (error.response) {
        // Erreur spécifique côté serveur
        console.error("Erreur serveur:", error.response.data);
        setError(
          error.response?.data?.message || "Erreur lors de la suppression"
        );
      } else if (error.request) {
        // Erreur de connexion (pas de réponse)
        console.error("Erreur de connexion:", error.request);
        setError("Erreur de connexion. Veuillez réessayer.");
      } else {
        // Erreur inconnue
        console.error("Erreur inconnue:", error.message);
        setError("Erreur inconnue lors de la suppression.");
      }
    }
  };

  const handleEdit = async () => {
    if (!selectedAbonnement) return;

    try {
      const formData = new FormData();
      formData.append("id", selectedAbonnement.id);
      formData.append("nom", editedNom);
      formData.append("description", editedDescription);
      formData.append("prix", editedPrix);
      formData.append("duree", editedDuree);
      formData.append("typeId", editedTypeId);

      if (selectedImageFile) {
        formData.append("image", selectedImageFile);
      }

      selectedAbonnement.matiereIds?.forEach((id) =>
        formData.append("matiereIds", id)
      );

      const response = await axiosInstance.put(
        `/api/abonnements/update/${selectedAbonnement.id}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      // Update the abonnement in the state
      setAbonnements((prev) =>
        prev.map((a) => (a.id === selectedAbonnement.id ? response.data : a))
      );

      setToastMessage("Abonnement modifié avec succès !");
      setShowToast(true);
      setTimeout(() => setShowToast(false), 4000);

      setShowEditModal(false);
      setError("");
    } catch (error) {
      console.error("Erreur lors de la modification:", error);
      setError(
        error.response?.data?.message || "Erreur lors de la modification"
      );
    }
  };

  // Filtrage des abonnements selon le terme de recherche
  const abonnementsFiltres = abonnements.filter(
    (abonnement) =>
      abonnement.nom &&
      abonnement.nom.toLowerCase().includes(searchTerm.toLowerCase())
  );
  // ✅ Pagination : Calcul des index de début et de fin des abonnements affichés
  const indexOfLastAbonnement = currentPage * abonnementsParPage;
  const indexOfFirstAbonnement = indexOfLastAbonnement - abonnementsParPage;
  const abonnementsAffiches = abonnements.slice(
    indexOfFirstAbonnement,
    indexOfLastAbonnement
  );

  // ✅ Fonction pour aller à la page suivante
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // ✅ Fonction pour revenir à la page précédente
  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const openEditModal = (abonnement) => {
    setSelectedAbonnement(abonnement);
    setEditedNom(abonnement.nom);
    setEditedDescription(abonnement.description);
    setEditedPrix(abonnement.prix.toString());
    setEditedDuree(abonnement.duree ? abonnement.duree.toString() : "");
    setEditedTypeId(abonnement.type?.id || "");
    setShowEditModal(true);
  };

  const openDeleteModal = (abonnement) => {
    setSelectedAbonnement(abonnement);
    setShowDeleteModal(true);
  };

  const loadImage = async (imageId) => {
    try {
      const response = await axiosInstance.get(
        `/api/imageAbonnement/load/${imageId}`,
        {
          responseType: "blob",
          headers: {
            Accept: "image/*",
          },
        }
      );
      const blobUrl = URL.createObjectURL(new Blob([response.data]));
      setImageUrls((prev) => ({ ...prev, [imageId]: blobUrl }));
    } catch (error) {
      console.error("Error loading image:", error);
      setImageUrls((prev) => ({
        ...prev,
        [imageId]: prev[imageId] || "placeholder-image.jpg",
      }));
    }
  };
  useEffect(() => {
    document.body.style.backgroundColor = darkMode ? "#1D1D1B" : "#fff";
    document.body.style.color = darkMode ? "#fff" : "#1D1D1B";
  }, [darkMode]);

  useEffect(() => {
    // Cleanup function to revoke blob URLs
    return () => {
      Object.values(imageUrls).forEach((url) => URL.revokeObjectURL(url));
    };
  }, [imageUrls]);

  // Fetch subscription types
  useEffect(() => {
    const fetchSubscriptionTypes = async () => {
      try {
        setLoadingTypes(true);
        const types = await abonnementTypeService.getAllTypes();
        setSubscriptionTypes(types);
        setTypeError(null);
      } catch (err) {
        console.error("Error fetching subscription types:", err);
        setTypeError("Erreur lors du chargement des types d'abonnement");
      } finally {
        setLoadingTypes(false);
      }
    };

    fetchSubscriptionTypes();
  }, []);

  return (
    <div className="position-relative">
      <div
        className="container-fluid px-4"
        style={{ backgroundColor: "#F6F4EE", minHeight: "100vh" }}
      >
        <div className="row page-titles mx-0 d-flex align-items-center justify-content-between flex-wrap">
          <div className="col-auto">
            <h4 className="fw-bold" style={{ color: "#000080" }}>
              Tous les Abonnements
            </h4>
          </div>

          <div className="col-md-4">
            <Form.Control
              type="text"
              placeholder="🔍 Rechercher un Abonnement..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="shadow-sm rounded-pill px-3"
              style={{
                backgroundColor: "#EEF9F5",
                borderColor: "#B7B7B7",
                color: "#1D1D1B",
              }}
            />
          </div>

          <div className="col-auto">
            <button
              className="btn rounded-pill shadow-sm px-4"
              style={{
                backgroundColor: "#F2BC00",
                color: "#1D1D1B",
                border: "none",
              }}
              onClick={() => navigate("/abonnements/ajouter")}
            >
              + Ajouter un Abonnement
            </button>
          </div>
        </div>

        {error && <div className="alert alert-danger">{error}</div>}

        <div className="row">
          {abonnementsFiltres.length === 0 ? (
            <p className="text-muted">Aucun abonnement trouvé.</p>
          ) : (
            abonnementsFiltres.map((abonnement) => (
              <div key={abonnement.id} className="col-lg-4 col-md-6 mb-4">
                <div className="card shadow-sm h-100 border-0 hover-shadow">
                  {abonnement.imageAbonnement ? (
                    <img
                      className="card-img-top"
                      src={
                        imageUrls[abonnement.imageAbonnement.idImage] ||
                        "placeholder-image.jpg"
                      }
                      alt={abonnement.nom}
                      style={{
                        height: "250px",
                        objectFit: "cover",
                        borderTopLeftRadius: "0.5rem",
                        borderTopRightRadius: "0.5rem",
                      }}
                      onLoad={() =>
                        !imageUrls[abonnement.imageAbonnement.idImage] &&
                        loadImage(abonnement.imageAbonnement.idImage)
                      }
                      onError={(e) => {
                        // Retry loading once on error
                        if (!e.target.retryAttempted) {
                          e.target.retryAttempted = true;
                          loadImage(abonnement.imageAbonnement.idImage);
                        }
                      }}
                    />
                  ) : (
                    <p className="text-muted text-center">Pas d'image</p>
                  )}
                  <div className="card-body">
                    <h5 className="card-title" style={{ color: "#37A7DF" }}>
                      {abonnement.nom}
                    </h5>
                    <p style={{ color: "#1D1D1B" }}>
                      <strong>Description :</strong> {abonnement.description}
                    </p>
                    <p style={{ color: "#1D1D1B" }}>
                      <strong>Prix :</strong> {abonnement.prix} DT
                    </p>
                    <p style={{ color: "#1D1D1B" }}>
                      <strong>Durée :</strong> {abonnement.duree} {abonnement.duree > 1 ? 'mois' : 'mois'}
                    </p>

                    {/* Subscription Type Information */}
                    {abonnement.type && (
                      <div className="mb-3">
                        <div className="d-flex align-items-center mb-2">
                          <strong className="me-2">Type :</strong>
                          <Badge
                            bg={
                              abonnement.type.nom === "Premium" ? "primary" :
                              abonnement.type.nom === "Standard" ? "success" :
                              "warning"
                            }
                            className="py-2 px-3"
                          >
                            {abonnement.type.nom}
                          </Badge>
                        </div>

                        <div className="small text-muted mb-2">{abonnement.type.description}</div>

                        <div className="d-flex flex-wrap gap-2 mt-1">
                          <span className={`badge ${abonnement.type.hasCourses ? 'bg-success' : 'bg-secondary'}`}>
                            <i className={`fa ${abonnement.type.hasCourses ? 'fa-check' : 'fa-times'} me-1`}></i> Cours
                          </span>
                          <span className={`badge ${abonnement.type.hasRecordings ? 'bg-success' : 'bg-secondary'}`}>
                            <i className={`fa ${abonnement.type.hasRecordings ? 'fa-check' : 'fa-times'} me-1`}></i> Enregistrements
                          </span>
                          <span className={`badge ${abonnement.type.hasLiveSessions ? 'bg-success' : 'bg-secondary'}`}>
                            <i className={`fa ${abonnement.type.hasLiveSessions ? 'fa-check' : 'fa-times'} me-1`}></i> Sessions Live
                          </span>
                        </div>
                      </div>
                    )}

                    <div
                      className="position-absolute"
                      style={{
                        bottom: "15px",
                        right: "15px",
                        display: "flex",
                        gap: "10px",
                      }}
                    >
                      <button
                        className="btn"
                        style={{
                          backgroundColor: "#F2BC00",
                          color: "#1D1D1B",
                          borderRadius: "12px",
                          width: "40px",
                          height: "40px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
                        }}
                        onClick={() => openEditModal(abonnement)}
                        title="Modifier"
                      >
                        <i className="la la-edit"></i>
                      </button>
                      <button
                        className="btn"
                        style={{
                          backgroundColor: "#D9534F",
                          color: "#fff",
                          borderRadius: "12px",
                          width: "40px",
                          height: "40px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
                        }}
                        onClick={() => openDeleteModal(abonnement)}
                        title="Supprimer"
                      >
                        <i className="la la-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}

          <div className="d-flex justify-content-center mt-4 mb-3 flex-wrap">
            <div className="pagination-container" style={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "white",
              borderRadius: "30px",
              padding: "5px",
              boxShadow: "0 4px 10px rgba(0, 0, 0, 0.1)"
            }}>
              <button
                style={{
                  backgroundColor: currentPage === 1 ? "#f0f0f0" : "var(--primary-blue)",
                  color: currentPage === 1 ? "#999" : "white",
                  border: "none",
                  borderRadius: "25px",
                  padding: "8px 20px",
                  margin: "0 5px",
                  fontWeight: "500",
                  cursor: currentPage === 1 ? "not-allowed" : "pointer",
                  transition: "all 0.3s ease",
                  display: "flex",
                  alignItems: "center"
                }}
                onClick={goToPrevPage}
                disabled={currentPage === 1}
              >
                <i className="la la-angle-left me-1" style={{ fontSize: "1.2rem" }}></i> Précédent
              </button>

              <div style={{
                backgroundColor: "var(--primary-yellow)",
                color: "var(--primary-dark)",
                borderRadius: "20px",
                padding: "6px 15px",
                margin: "0 10px",
                fontWeight: "600",
                fontSize: "0.9rem"
              }}>
                Page {currentPage} sur {totalPages}
              </div>

              <button
                style={{
                  backgroundColor: currentPage === totalPages ? "#f0f0f0" : "var(--primary-blue)",
                  color: currentPage === totalPages ? "#999" : "white",
                  border: "none",
                  borderRadius: "25px",
                  padding: "8px 20px",
                  margin: "0 5px",
                  fontWeight: "500",
                  cursor: currentPage === totalPages ? "not-allowed" : "pointer",
                  transition: "all 0.3s ease",
                  display: "flex",
                  alignItems: "center"
                }}
                onClick={goToNextPage}
                disabled={currentPage === totalPages}
              >
                Suivant <i className="la la-angle-right ms-1" style={{ fontSize: "1.2rem" }}></i>
              </button>
            </div>
          </div>
        </div>

        {/* Modale de suppression */}
        <Modal
          show={showDeleteModal}
          onHide={() => setShowDeleteModal(false)}
          centered
          size="sm"
          contentClassName="border-0 shadow"
          backdropClassName="bg-dark bg-opacity-75"
        >
          <Modal.Header
            closeButton
            style={{
              backgroundColor: "var(--primary-navy)",
              color: "white",
              border: "none",
              borderTopLeftRadius: "8px",
              borderTopRightRadius: "8px"
            }}
          >
            <Modal.Title className="fs-5">
              <i className="la la-exclamation-triangle me-2"></i>
              Confirmer la suppression
            </Modal.Title>
          </Modal.Header>
          <Modal.Body style={{ padding: "20px" }}>
            <div className="text-center mb-3">
              <div style={{
                width: "60px",
                height: "60px",
                borderRadius: "50%",
                backgroundColor: "rgba(220, 53, 69, 0.1)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                margin: "0 auto 15px auto"
              }}>
                <i className="la la-trash" style={{ fontSize: "2rem", color: "#dc3545" }}></i>
              </div>
            </div>
            <p className="mb-0 text-center" style={{ fontSize: "1rem" }}>
              Êtes-vous sûr de vouloir supprimer l'abonnement{" "}
              <strong style={{ color: "var(--primary-blue)" }}>{selectedAbonnement?.nom}</strong> ?
            </p>
            <p className="text-muted text-center mt-2 small">Cette action est irréversible.</p>
          </Modal.Body>
          <Modal.Footer className="justify-content-center border-0" style={{ padding: "0 20px 20px 20px" }}>
            <Button
              style={{
                backgroundColor: "transparent",
                borderColor: "#6c757d",
                color: "#6c757d",
                borderRadius: "6px",
                padding: "8px 20px",
                fontWeight: "500",
                transition: "all 0.3s ease",
                marginRight: "10px"
              }}
              onClick={() => setShowDeleteModal(false)}
              className="w-100 w-sm-auto"
            >
              <i className="la la-times me-2"></i> Annuler
            </Button>
            <Button
              style={{
                backgroundColor: "#dc3545",
                borderColor: "#dc3545",
                color: "white",
                borderRadius: "6px",
                padding: "8px 20px",
                fontWeight: "500",
                transition: "all 0.3s ease",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
              }}
              onClick={handleDelete}
              className="w-100 w-sm-auto mt-2 mt-sm-0"
            >
              <i className="la la-trash me-2"></i> Supprimer
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Modale de modification */}
        <Modal
          show={showEditModal}
          onHide={() => setShowEditModal(false)}
          centered
          contentClassName="border-0 shadow"
          backdropClassName="bg-dark bg-opacity-75"
        >
          <Modal.Header
            closeButton
            style={{
              backgroundColor: "var(--primary-blue)",
              color: "white",
              border: "none",
              padding: "15px 20px"
            }}
          >
            <Modal.Title style={{ fontWeight: "600" }}>
              <i className="la la-edit me-2"></i>
              Modifier l'abonnement
            </Modal.Title>
          </Modal.Header>
          <Modal.Body style={{ padding: "20px" }}>
            <Form>
              <Form.Group controlId="formFile" className="mb-3">
                <Form.Label>Image</Form.Label>
                <Form.Control
                  type="file"
                  accept="image/*"
                  onChange={(e) => setSelectedImageFile(e.target.files[0])}
                  className="form-control"
                  style={{ padding: "8px", borderRadius: "6px" }}
                />
                <small className="text-muted">Format recommandé: JPG, PNG. Max 10MB</small>
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Nom</Form.Label>
                <Form.Control
                  type="text"
                  value={editedNom}
                  onChange={(e) => setEditedNom(e.target.value)}
                  className="form-control"
                  style={{ padding: "8px", borderRadius: "6px" }}
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Description</Form.Label>
                <Form.Control
                  as="textarea"
                  value={editedDescription}
                  onChange={(e) => setEditedDescription(e.target.value)}
                  className="form-control"
                  style={{ padding: "8px", borderRadius: "6px", minHeight: "100px" }}
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Prix</Form.Label>
                <Form.Control
                  type="number"
                  value={editedPrix}
                  onChange={(e) => setEditedPrix(e.target.value)}
                  className="form-control"
                  style={{ padding: "8px", borderRadius: "6px" }}
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Durée (mois)</Form.Label>
                <Form.Control
                  type="number"
                  value={editedDuree}
                  onChange={(e) => setEditedDuree(e.target.value)}
                  className="form-control"
                  style={{ padding: "8px", borderRadius: "6px" }}
                />
              </Form.Group>

              {/* Type d'abonnement */}
              <Form.Group className="mb-3">
                <Form.Label>Type d'abonnement</Form.Label>
                {loadingTypes ? (
                  <div className="d-flex align-items-center">
                    <Spinner animation="border" size="sm" className="me-2" />
                    <span>Chargement des types d'abonnement...</span>
                  </div>
                ) : typeError ? (
                  <div className="text-danger">{typeError}</div>
                ) : (
                  <Form.Select
                    value={editedTypeId}
                    onChange={(e) => setEditedTypeId(e.target.value)}
                    className="form-control"
                    style={{ padding: "8px", borderRadius: "6px" }}
                  >
                    <option value="">Sélectionnez un type d'abonnement</option>
                    {subscriptionTypes.map(type => (
                      <option key={type.id} value={type.id}>
                        {type.nom} - {type.description}
                      </option>
                    ))}
                  </Form.Select>
                )}

                {editedTypeId && subscriptionTypes.length > 0 && (
                  <div className="mt-2 p-3 border rounded bg-light">
                    <h6>Caractéristiques du type sélectionné:</h6>
                    {subscriptionTypes.filter(type => type.id == editedTypeId).map(type => (
                      <div key={type.id} className="d-flex flex-wrap gap-2 mt-2">
                        <span className={`badge ${type.hasCourses ? 'bg-success' : 'bg-secondary'}`}>
                          <i className={`fa ${type.hasCourses ? 'fa-check' : 'fa-times'} me-1`}></i> Cours
                        </span>
                        <span className={`badge ${type.hasRecordings ? 'bg-success' : 'bg-secondary'}`}>
                          <i className={`fa ${type.hasRecordings ? 'fa-check' : 'fa-times'} me-1`}></i> Enregistrements
                        </span>
                        <span className={`badge ${type.hasLiveSessions ? 'bg-success' : 'bg-secondary'}`}>
                          <i className={`fa ${type.hasLiveSessions ? 'fa-check' : 'fa-times'} me-1`}></i> Sessions Live
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer style={{
            borderTop: "1px solid #eee",
            padding: "15px 20px",
            display: "flex",
            justifyContent: "space-between"
          }}>
            <Button
              style={{
                backgroundColor: "transparent",
                borderColor: "#dc3545",
                color: "#dc3545",
                borderRadius: "6px",
                padding: "8px 20px",
                fontWeight: "500",
                transition: "all 0.3s ease"
              }}
              onClick={() => setShowEditModal(false)}
            >
              <i className="la la-times me-2"></i> Annuler
            </Button>
            <Button
              style={{
                backgroundColor: "var(--primary-blue)",
                borderColor: "var(--primary-blue)",
                color: "white",
                borderRadius: "6px",
                padding: "8px 20px",
                fontWeight: "500",
                transition: "all 0.3s ease",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
              }}
              onClick={handleEdit}
            >
              <i className="la la-save me-2"></i> Enregistrer
            </Button>
          </Modal.Footer>
        </Modal>
        {showToast && (
          <div
            className="toast show position-fixed bottom-0 end-0 m-3"
            style={{
              zIndex: 9999,
              backgroundColor: "white",
              color: "var(--primary-dark)",
              borderRadius: "8px",
              boxShadow: "0 5px 15px rgba(0,0,0,0.2)",
              border: "none",
              overflow: "hidden",
              minWidth: "300px"
            }}
          >
            <div className="toast-header" style={{
              backgroundColor: "var(--primary-blue)",
              color: "white",
              borderBottom: "none",
              padding: "10px 15px"
            }}>
              <i className="la la-bell me-2"></i>
              <strong className="me-auto">Notification</strong>
              <button
                type="button"
                className="btn-close btn-close-white"
                onClick={() => setShowToast(false)}
              ></button>
            </div>
            <div className="toast-body" style={{ padding: "15px" }}>
              <i className="la la-check-circle me-2" style={{ color: "var(--primary-green)" }}></i>
              {toastMessage}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Affichage;
