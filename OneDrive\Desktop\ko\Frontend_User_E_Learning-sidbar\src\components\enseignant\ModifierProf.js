import React, { useState, useEffect } from "react";
import { useKeycloak } from '@react-keycloak/web';
import axios from "axios";

const ModifierProf = ({ enseignantId }) => {
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [matieres, setMatieres] = useState([]);
  const [niveaux, setNiveaux] = useState([]);
  const [selectedMatieres, setSelectedMatieres] = useState([]);
  const [selectedNiveaux, setSelectedNiveaux] = useState([]);

  // Etat pour stocker les valeurs du formulaire
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    specialty: "",
  });

  // Fonction pour afficher/masquer le mot de passe
  const togglePasswordVisibility = () => {
    setPasswordVisible(!passwordVisible);
  };

  // Fonction pour gérer les changements dans les champs du formulaire
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  // Effet pour charger les matières et niveaux
  const { keycloak } = useKeycloak();
  const isEnseignant = keycloak.hasResourceRole('ENSEIGNANT');
  const isAdmin = keycloak.hasResourceRole('ADMIN');

  useEffect(() => {
    const fetchData = async () => {
      try {
        const niveauxEndpoint = isEnseignant ? '/api/niveaux/enseignant' : '/api/niveaux/all';
        const [matieresRes, niveauxRes] = await Promise.all([
          axios.get('/api/matieres'),
          axios.get(niveauxEndpoint)
        ]);
        setMatieres(matieresRes.data);
        setNiveaux(niveauxRes.data);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };
    fetchData();
  }, []);

  // Effet pour charger les données de l'enseignant
  useEffect(() => {
    const fetchEnseignantData = async () => {
      if (!enseignantId) return;
      
      try {
        const response = await axios.get(`/api/enseignants/${enseignantId}`);
        const enseignant = response.data;
        setFormData({
          firstName: enseignant.firstName,
          lastName: enseignant.lastName,
          email: enseignant.email,
          specialty: enseignant.specialty,
          password: "" // Don't set the password from API for security
        });
        setSelectedMatieres(enseignant.matieres?.map(m => m.id) || []);
        setSelectedNiveaux(enseignant.niveaux?.map(n => n.id) || []);
      } catch (error) {
        console.error('Error fetching enseignant:', error);
      }
    };

    fetchEnseignantData();
  }, [enseignantId]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await axios.put(`/api/enseignants/${enseignantId}`, {
        ...formData,
        matiereIds: selectedMatieres,
        niveauIds: selectedNiveaux
      });
      // Handle success (redirect or show message)
    } catch (error) {
      console.error('Error updating enseignant:', error);
    }
  };

  return (
    <div>
        <div className="container-fluid">
          <div className="row page-titles mx-0">
            <div className="col-sm-6 p-md-0">
              <div className="welcome-text">
                <h4>Modifier un enseignant</h4>
              </div>
            </div>
            <div className="col-sm-6 p-md-0 justify-content-sm-end mt-2 mt-sm-0 d-flex">
              <ol className="breadcrumb">
                <li className="breadcrumb-item">
                  <a href="#">Enseignant</a>
                </li>
                <li className="breadcrumb-item active">
                  <a href="#">Modifier</a>
                </li>
              </ol>
            </div>
          </div>

          <div className="row">
            <div className="col-xl-12 col-xxl-12 col-sm-12">
              <div className="card">
                <div className="card-header">
                  <h5 className="card-title">Informations de base</h5>
                </div>
                <div className="card-body">
                  <form onSubmit={handleSubmit}>
                    <div className="row">
                      <div className="col-lg-6 col-md-6 col-sm-12">
                        <div className="form-group">
                          <label className="form-label">Prénom</label>
                          <input
                            type="text"
                            name="firstName"
                            className="form-control"
                            value={formData.firstName}
                            onChange={handleInputChange}
                            required
                          />
                        </div>
                      </div>
                      <div className="col-lg-6 col-md-6 col-sm-12">
                        <div className="form-group">
                          <label className="form-label">Nom</label>
                          <input
                            type="text"
                            name="lastName"
                            className="form-control"
                            value={formData.lastName}
                            onChange={handleInputChange}
                            required
                          />
                        </div>
                      </div>
                      <div className="col-lg-6 col-md-6 col-sm-12">
                        <div className="form-group">
                          <label className="form-label">Email</label>
                          <input
                            type="email"
                            name="email"
                            className="form-control"
                            value={formData.email}
                            onChange={handleInputChange}
                            required
                          />
                        </div>
                      </div>
                      <div className="col-lg-6 col-md-6 col-sm-12">
                        <div className="form-group">
                          <label className="form-label">Spécialité</label>
                          <input
                            type="text"
                            name="specialty"
                            className="form-control"
                            value={formData.specialty}
                            onChange={handleInputChange}
                          />
                        </div>
                      </div>
                      <div className="col-lg-6 col-md-6 col-sm-12">
                        <div className="form-group">
                          <label className="form-label">Matières</label>
                          <select
                            multiple
                            className="form-control"
                            value={selectedMatieres}
                            onChange={(e) => setSelectedMatieres(Array.from(e.target.selectedOptions, option => Number(option.value)))}
                          >
                            {matieres.map(matiere => (
                              <option key={matiere.id} value={matiere.id}>
                                {matiere.name}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                      <div className="col-lg-6 col-md-6 col-sm-12">
                        <div className="form-group">
                          <label className="form-label">Niveaux</label>
                          <select
                            multiple
                            className="form-control"
                            value={selectedNiveaux}
                            onChange={(e) => setSelectedNiveaux(Array.from(e.target.selectedOptions, option => Number(option.value)))}
                          >
                            {niveaux.map(niveau => (
                              <option key={niveau.id} value={niveau.id}>
                                {niveau.name}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                      <div className="col-lg-12 col-md-12 col-sm-12">
                        <button type="submit" className="btn btn-primary">
                          Modifier
                        </button>
                        <button
                          type="button"
                          className="btn btn-light ml-2"
                          onClick={() => window.history.back()}
                        >
                          Annuler
                        </button>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
  );
};

export default ModifierProf;
