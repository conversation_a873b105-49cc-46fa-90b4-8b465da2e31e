import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Modal, Button, Form, Pagination } from "react-bootstrap";
import axiosInstance from "../../services/axiosService";

const AfficherChapitre = () => {
  const navigate = useNavigate();
  const [chapitres, setChapitres] = useState([]);
  const [chapitreModif, setChapitreModif] = useState(null);
  const [chapitreSuppr, setChapitreSuppr] = useState(null);
  const [nouveauNom, setNouveauNom] = useState("");
  const [nouveauProf, setNouveauProf] = useState("");
  const [nouvelleDuree, setNouvelleDuree] = useState(0);
  const [nouveauNombreDeCours, setNouveauNombreDeCours] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);
  const itemsPerPage = 8;

  const [searchTerm, setSearchTerm] = useState("");

  const [matiereId, setMatiereId] = useState("");
  const [niveauId, setNiveauId] = useState("");

  // Met à jour les valeurs dès que chapitreModif est dispo
  useEffect(() => {
    if (chapitreModif?.matiereNiveau) {
      setMatiereId(chapitreModif.matiereNiveau.matiere?.id || "");
      setNiveauId(chapitreModif.matiereNiveau.niveau?.id || "");
    }
  }, [chapitreModif]);
  // Exemple d'initialisation si ce n'est pas déjà fait :
  const [matieres, setMatieres] = useState([]);
  const [niveaux, setNiveaux] = useState([]);

  // Fetch à faire au useEffect quand le modal s'ouvre ou au mount
  useEffect(() => {
    const fetchData = async () => {
      const matieresRes = await axiosInstance.get("/api/matieres");
      const niveauxRes = await axiosInstance.get("/api/niveaux/all");
      setMatieres(matieresRes.data);
      setNiveaux(niveauxRes.data);
    };
    if (chapitreModif) fetchData();
  }, [chapitreModif]);

  useEffect(() => {
    if (searchTerm) {
      axiosInstance
        .get(`/api/chapitres/search?nomChapitre=${searchTerm}`)
        .then((response) => {
          setChapitres(response.data);
          setTotalPages(1);
          setTotalElements(response.data.length);
        })
        .catch((err) => setError(err.message));
    } else {
      fetchChapitres();
    }
  }, [currentPage, searchTerm]);

  const fetchChapitres = async () => {
    try {
      const response = await axiosInstance.get(
        `/api/chapitres/page?page=${currentPage}&size=${itemsPerPage}`
      );
      setChapitres(response.data.content);
      setTotalPages(response.data.totalPages);
      setTotalElements(response.data.totalElements);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const supprimerChapitre = async () => {
    try {
      await axiosInstance.delete(`/api/chapitres/${chapitreSuppr.id}`, {
        params: {
          matiereId: chapitreSuppr.matiereId,
          niveauId: chapitreSuppr.niveauId,
        },
      });
      setChapitres(chapitres.filter((chap) => chap.id !== chapitreSuppr.id));
      setChapitreSuppr(null);
    } catch (err) {
      setError(err.message);
    }
  };

  const modifierChapitre = async () => {
    try {
      await axiosInstance.put(
        `/api/chapitres/${chapitreModif.id}`,
        {
          nomChapitre: nouveauNom,
          nomDeProf: nouveauProf,
          duree: nouvelleDuree,
          nombreDeCours: nouveauNombreDeCours,
          // Ajout de la relation matiereNiveau

        },
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      // Mise à jour locale
      setChapitres(
        chapitres.map((chap) =>
          chap.id === chapitreModif.id
            ? {
                ...chap,
                nomChapitre: nouveauNom,
                nomDeProf: nouveauProf,
                duree: nouvelleDuree,
                nombreDeCours: nouveauNombreDeCours,


              }
            : chap
        )
      );
      setChapitreModif(null);
    } catch (err) {
      setError(err.message);
    }
  };

  const renderPagination = () => {
    return (
      <div className="d-flex justify-content-center mt-4 mb-3 flex-wrap">
        <div className="pagination-container" style={{
          display: "flex",
          alignItems: "center",
          backgroundColor: "white",
          borderRadius: "30px",
          padding: "5px",
          boxShadow: "0 4px 10px rgba(0, 0, 0, 0.1)"
        }}>
          <button
            style={{
              backgroundColor: currentPage === 0 ? "#f0f0f0" : "var(--primary-blue)",
              color: currentPage === 0 ? "#999" : "white",
              border: "none",
              borderRadius: "25px",
              padding: "8px 20px",
              margin: "0 5px",
              fontWeight: "500",
              cursor: currentPage === 0 ? "not-allowed" : "pointer",
              transition: "all 0.3s ease",
              display: "flex",
              alignItems: "center"
            }}
            onClick={() => setCurrentPage(currentPage - 1)}
            disabled={currentPage === 0}
          >
            <i className="la la-angle-left me-1" style={{ fontSize: "1.2rem" }}></i> Précédent
          </button>

          <div style={{
            backgroundColor: "var(--primary-yellow)",
            color: "var(--primary-dark)",
            borderRadius: "20px",
            padding: "6px 15px",
            margin: "0 10px",
            fontWeight: "600",
            fontSize: "0.9rem"
          }}>
            Page {currentPage + 1} sur {totalPages}
          </div>

          <button
            style={{
              backgroundColor: currentPage === totalPages - 1 ? "#f0f0f0" : "var(--primary-blue)",
              color: currentPage === totalPages - 1 ? "#999" : "white",
              border: "none",
              borderRadius: "25px",
              padding: "8px 20px",
              margin: "0 5px",
              fontWeight: "500",
              cursor: currentPage === totalPages - 1 ? "not-allowed" : "pointer",
              transition: "all 0.3s ease",
              display: "flex",
              alignItems: "center"
            }}
            onClick={() => setCurrentPage(currentPage + 1)}
            disabled={currentPage === totalPages - 1}
          >
            Suivant <i className="la la-angle-right ms-1" style={{ fontSize: "1.2rem" }}></i>
          </button>
        </div>
      </div>
    );
  };

  const ouvrirFormModif = (chapitre) => {
    setChapitreModif(chapitre);
    setNouveauNom(chapitre.nomChapitre);
    setNouveauProf(chapitre.nomDeProf);
    setNouvelleDuree(chapitre.duree);
    setNouveauNombreDeCours(chapitre.nombreDeCours);
  };

  return (
    <div className="container-fluid">
      <div className="row page-titles mx-0 d-flex align-items-center justify-content-between">
        <div className="col-auto">
          <h4 style={{ color: "#37A7DF" }}>Tous Les Chapitres</h4>
          <p>
            Total: {searchTerm ? chapitres.length : totalElements} chapitres
          </p>
        </div>
        <div className="col-md-4">
          <Form.Control
            type="text"
            placeholder="Rechercher un chapitre..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(0);
            }}
          />
        </div>
        <div className="col-auto">
          <button
            className="btn btn-primary"
            style={{ backgroundColor: "#37A7DF", borderColor: "#37A7DF" }}
            onClick={() => navigate("/chapitres/ajouter")}
          >
            + Ajouter un Chapitre
          </button>
        </div>
      </div>

      {error && <div className="alert alert-danger">{error}</div>}

      {loading ? (
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="sr-only">Chargement...</span>
          </div>
        </div>
      ) : (
        <div className="row g-3">
          {chapitres.length > 0 ? (
            chapitres.map((chapitre) => (
              <div className="col-lg-3 col-md-4 col-sm-6" key={chapitre.id}>
                <div className="card shadow-sm border-0 rounded-lg">
                  <div className="card-body text-center p-2">
                    <h6 className="fw-bold">Titre du chapitre :</h6>
                    <h4>{chapitre.nomChapitre}</h4>
                    <p>
                      <strong>Professeur :</strong> {chapitre.nomDeProf}
                    </p>
                    <p>
                      <strong>Durée :</strong> {chapitre.duree} heures
                    </p>
                    <p>
                      <strong>Nombre de cours :</strong>{" "}
                      {chapitre.nombreDeCours}
                    </p>
                    <button
                      className="btn mt-2 w-100 btn-sm"
                      onClick={() => ouvrirFormModif(chapitre)}
                      style={{ backgroundColor: "#F2BC00", color: "white" }}
                    >
                      Modifier
                    </button>
                    <button
                      className="btn btn-danger light mt-2 w-100 btn-sm"
                      onClick={() => setChapitreSuppr(chapitre)}
                    >
                      Supprimer
                    </button>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <p className="text-center text-muted">
              {searchTerm
                ? "Aucun résultat trouvé"
                : "Aucun chapitre disponible"}
            </p>
          )}
        </div>
      )}

      {renderPagination()}

      {/* Modal Modification */}
      {chapitreModif && (
        <Modal
          show
          onHide={() => setChapitreModif(null)}
          centered
          contentClassName="border-0 shadow"
          backdropClassName="bg-dark bg-opacity-75"
        >
          <Modal.Header
            closeButton
            style={{
              backgroundColor: "var(--primary-blue)",
              color: "white",
              border: "none",
              padding: "15px 20px"
            }}
          >
            <Modal.Title style={{ fontWeight: "600" }}>
              <i className="la la-edit me-2"></i>
              Modifier le chapitre
            </Modal.Title>
          </Modal.Header>
          <Modal.Body style={{ padding: "20px" }}>
            <Form>
              <Form.Group className="mb-3">
                <Form.Label>Nom du chapitre</Form.Label>
                <Form.Control
                  type="text"
                  value={nouveauNom}
                  onChange={(e) => setNouveauNom(e.target.value)}
                  className="form-control"
                  style={{ padding: "8px", borderRadius: "6px" }}
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Professeur</Form.Label>
                <Form.Control
                  type="text"
                  value={nouveauProf}
                  onChange={(e) => setNouveauProf(e.target.value)}
                  className="form-control"
                  style={{ padding: "8px", borderRadius: "6px" }}
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Durée (heures)</Form.Label>
                <Form.Control
                  type="number"
                  value={nouvelleDuree}
                  onChange={(e) => setNouvelleDuree(e.target.value)}
                  className="form-control"
                  style={{ padding: "8px", borderRadius: "6px" }}
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Nombre de cours</Form.Label>
                <Form.Control
                  type="number"
                  value={nouveauNombreDeCours}
                  onChange={(e) => setNouveauNombreDeCours(e.target.value)}
                  className="form-control"
                  style={{ padding: "8px", borderRadius: "6px" }}
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer style={{
            borderTop: "1px solid #eee",
            padding: "15px 20px",
            display: "flex",
            justifyContent: "space-between"
          }}>
            <Button
              style={{
                backgroundColor: "transparent",
                borderColor: "#dc3545",
                color: "#dc3545",
                borderRadius: "6px",
                padding: "8px 20px",
                fontWeight: "500",
                transition: "all 0.3s ease"
              }}
              onClick={() => setChapitreModif(null)}
            >
              <i className="la la-times me-2"></i> Annuler
            </Button>
            <Button
              style={{
                backgroundColor: "var(--primary-blue)",
                borderColor: "var(--primary-blue)",
                color: "white",
                borderRadius: "6px",
                padding: "8px 20px",
                fontWeight: "500",
                transition: "all 0.3s ease",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
              }}
              onClick={modifierChapitre}
            >
              <i className="la la-save me-2"></i> Enregistrer
            </Button>
          </Modal.Footer>
        </Modal>
      )}

      {/* Modal Suppression */}
      {chapitreSuppr && (
        <Modal
          show
          onHide={() => setChapitreSuppr(null)}
          centered
          size="sm"
          contentClassName="border-0 shadow"
          backdropClassName="bg-dark bg-opacity-75"
        >
          <Modal.Header
            closeButton
            style={{
              backgroundColor: "var(--primary-navy)",
              color: "white",
              border: "none",
              borderTopLeftRadius: "8px",
              borderTopRightRadius: "8px"
            }}
          >
            <Modal.Title className="fs-5">
              <i className="la la-exclamation-triangle me-2"></i>
              Confirmer la suppression
            </Modal.Title>
          </Modal.Header>
          <Modal.Body style={{ padding: "20px" }}>
            <div className="text-center mb-3">
              <div style={{
                width: "60px",
                height: "60px",
                borderRadius: "50%",
                backgroundColor: "rgba(220, 53, 69, 0.1)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                margin: "0 auto 15px auto"
              }}>
                <i className="la la-trash" style={{ fontSize: "2rem", color: "#dc3545" }}></i>
              </div>
            </div>
            <p className="mb-0 text-center" style={{ fontSize: "1rem" }}>
              Êtes-vous sûr de vouloir supprimer le chapitre{" "}
              <strong style={{ color: "var(--primary-blue)" }}>{chapitreSuppr.nomChapitre}</strong> ?
            </p>
            <p className="text-muted text-center mt-2 small">Cette action est irréversible.</p>
          </Modal.Body>
          <Modal.Footer className="justify-content-center border-0" style={{ padding: "0 20px 20px 20px" }}>
            <Button
              style={{
                backgroundColor: "transparent",
                borderColor: "#6c757d",
                color: "#6c757d",
                borderRadius: "6px",
                padding: "8px 20px",
                fontWeight: "500",
                transition: "all 0.3s ease",
                marginRight: "10px"
              }}
              onClick={() => setChapitreSuppr(null)}
              className="w-100 w-sm-auto"
            >
              <i className="la la-times me-2"></i> Annuler
            </Button>
            <Button
              style={{
                backgroundColor: "#dc3545",
                borderColor: "#dc3545",
                color: "white",
                borderRadius: "6px",
                padding: "8px 20px",
                fontWeight: "500",
                transition: "all 0.3s ease",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
              }}
              onClick={supprimerChapitre}
              className="w-100 w-sm-auto mt-2 mt-sm-0"
            >
              <i className="la la-trash me-2"></i> Supprimer
            </Button>
          </Modal.Footer>
        </Modal>
      )}
    </div>
  );
};

export default AfficherChapitre;
