import React from 'react'
import { useNavigate } from 'react-router-dom';
import { Button } from '@mui/material';

const Unauthorized = () => {
  const navigate = useNavigate();

  return (
    <div style={{
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      textAlign: 'center',
      backgroundColor: '#f8f9fa',
    }}>
      <h1 style={{ fontSize: '3rem', marginBottom: '1rem', color: '#dc3545' }}>403 - Accès refusé</h1>
      <p style={{ fontSize: '1.2rem', marginBottom: '2rem' }}>
        Vous n’avez pas les droits nécessaires pour accéder à cette page.
      </p>
      <Button variant="contained" color="primary" onClick={() => navigate('/dashboard')}>
        Retour au tableau de bord
      </Button>
    </div>
  );
};

 

export default Unauthorized
