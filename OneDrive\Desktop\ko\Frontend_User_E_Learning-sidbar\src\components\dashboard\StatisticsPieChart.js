/**
 * Composant StatisticsPieChart
 *
 * Ce composant affiche un graphique en secteurs (pie chart) montrant la répartition
 * des différentes entités du système éducatif.
 *
 * Fonctionnalités principales :
 * - Récupération des statistiques générales via l'API
 * - Graphique interactif avec tooltips et animations
 * - Légende personnalisée avec couleurs cohérentes
 * - Gestion des états de chargement et d'erreur
 * - Design responsive avec palette de couleurs du système
 *
 * <AUTHOR> équipe de développement
 * @version 1.0
 * @since 2025-01-24
 */

import React, { useEffect, useState } from "react";
import axiosInstance from "../../services/axiosService";
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
  Legend,
} from "recharts";

/**
 * Palette de couleurs pour le graphique en secteurs
 * Conforme aux spécifications du design system
 */
const CHART_COLORS = {
  COURS: "#37A7DF",        // Bleu principal
  ABONNEMENTS: "#F2BC00",  // Jaune/Orange
  NIVEAUX: "#248E39",      // Vert
  MATIERES: "#000080",     // Bleu foncé
  BACKGROUND: "#F6F4EE",   // Crème
  LIGHT_BG: "#EEF9F5",    // Vert très clair
  TEXT_DARK: "#1D1D1B",   // Noir/Gris foncé
  NEUTRAL: "#B7B7B7"      // Gris
};

/**
 * Configuration des couleurs pour chaque secteur du graphique
 */
const COLORS = [CHART_COLORS.COURS, CHART_COLORS.ABONNEMENTS, CHART_COLORS.NIVEAUX, CHART_COLORS.MATIERES];

/**
 * Composant principal pour afficher le graphique en secteurs des statistiques
 * @returns {JSX.Element} Le composant React
 */
const StatisticsPieChart = () => {
  // États du composant
  const [data, setData] = useState([]); // Données du graphique
  const [loading, setLoading] = useState(true); // État de chargement
  const [error, setError] = useState(null); // État d'erreur
  const [totalEntities, setTotalEntities] = useState(0); // Total des entités

  /**
   * Hook useEffect pour charger les données au montage du composant
   */
  useEffect(() => {
    /**
     * Fonction asynchrone pour récupérer les statistiques générales
     * Transforme les données en format adapté au PieChart
     */
    const fetchStatistics = async () => {
      try {
        setLoading(true);
        setError(null);

        // Récupération des statistiques depuis l'API
        const response = await axiosInstance.get("/api/statistics");
        console.log("📊 Statistics API Response:", response.data);

        const stats = response.data;

        // Transformation des données pour le PieChart
        const chartData = [
          {
            name: "Cours",
            value: stats.totalCours || 0,
            icon: "🎓",
            description: "Cours disponibles"
          },
          {
            name: "Abonnements",
            value: stats.totalAbonnements || 0,
            icon: "💳",
            description: "Types d'abonnements"
          },
          {
            name: "Niveaux",
            value: stats.totalNiveaux || 0,
            icon: "📊",
            description: "Niveaux d'étude"
          },
          {
            name: "Matières",
            value: stats.totalMatiere || 0,
            icon: "📚",
            description: "Matières enseignées"
          }
        ];

        // Filtrer les données avec valeur > 0 pour un graphique plus lisible
        const filteredData = chartData.filter(item => item.value > 0);

        // Calculer le total
        const total = chartData.reduce((sum, item) => sum + item.value, 0);

        setData(filteredData.length > 0 ? filteredData : chartData);
        setTotalEntities(total);

        console.log("✅ Formatted chart data:", filteredData);

      } catch (err) {
        console.error("❌ Erreur lors du chargement des statistiques:", err);
        setError(err.response?.data?.message || "Erreur de chargement des statistiques");
      } finally {
        setLoading(false);
      }
    };

    fetchStatistics();
  }, []);

  /**
   * Fonction personnalisée pour le rendu des tooltips
   */
  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div style={{
          backgroundColor: CHART_COLORS.BACKGROUND,
          border: `2px solid ${CHART_COLORS.COURS}`,
          borderRadius: "8px",
          padding: "12px",
          boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
          color: CHART_COLORS.TEXT_DARK
        }}>
          <p style={{ margin: "0 0 5px 0", fontWeight: "600" }}>
            {data.icon} {data.name}
          </p>
          <p style={{ margin: "0 0 5px 0", fontSize: "18px", fontWeight: "bold", color: payload[0].color }}>
            {data.value}
          </p>
          <p style={{ margin: "0", fontSize: "12px", fontStyle: "italic", color: CHART_COLORS.NEUTRAL }}>
            {data.description}
          </p>
          <p style={{ margin: "5px 0 0 0", fontSize: "11px", color: CHART_COLORS.NEUTRAL }}>
            {totalEntities > 0 ? `${((data.value / totalEntities) * 100).toFixed(1)}% du total` : ''}
          </p>
        </div>
      );
    }
    return null;
  };

  /**
   * Fonction personnalisée pour le rendu des labels sur le graphique
   */
  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    if (percent < 0.05) return null; // Masquer les labels pour les petits secteurs

    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill={CHART_COLORS.BACKGROUND}
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize="12"
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  /**
   * Rendu de l'état de chargement
   */
  if (loading) {
    return (
      <div style={{
        padding: "20px",
        textAlign: "center",
        backgroundColor: CHART_COLORS.BACKGROUND,
        borderRadius: "15px",
        boxShadow: "0 4px 15px rgba(0,0,0,0.1)"
      }}>
        <h5 style={{ color: CHART_COLORS.COURS, marginBottom: "20px" }}>
          🥧 Répartition des Entités du Système
        </h5>
        <div style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: "200px",
          color: CHART_COLORS.COURS
        }}>
          <div className="spinner-border me-2" role="status" aria-hidden="true"></div>
          Chargement des statistiques...
        </div>
      </div>
    );
  }

  /**
   * Rendu de l'état d'erreur
   */
  if (error) {
    return (
      <div style={{
        padding: "20px",
        backgroundColor: CHART_COLORS.BACKGROUND,
        borderRadius: "15px",
        boxShadow: "0 4px 15px rgba(0,0,0,0.1)"
      }}>
        <h5 style={{ color: CHART_COLORS.COURS, marginBottom: "20px" }}>
          🥧 Répartition des Entités du Système
        </h5>
        <div className="alert alert-danger" role="alert" style={{
          backgroundColor: "#fff",
          borderLeft: "4px solid #dc3545",
          borderRadius: "0 4px 4px 0"
        }}>
          <i className="fa fa-exclamation-triangle me-2"></i>
          {error}
        </div>
        <button
          className="btn btn-primary"
          onClick={() => window.location.reload()}
          style={{ backgroundColor: CHART_COLORS.COURS, borderColor: CHART_COLORS.COURS }}
        >
          <i className="fa fa-refresh me-2"></i>
          Réessayer
        </button>
      </div>
    );
  }

  /**
   * Rendu principal du composant
   */
  return (
    <div style={{
      backgroundColor: CHART_COLORS.BACKGROUND,
      borderRadius: "15px",
      padding: "25px",
      boxShadow: "0 8px 25px rgba(0,0,0,0.12)",
      border: "1px solid " + CHART_COLORS.LIGHT_BG,
      position: "relative",
      overflow: "hidden"
    }}>
      {/* Décoration d'arrière-plan */}
      <div style={{
        position: "absolute",
        top: "-30px",
        left: "-30px",
        width: "120px",
        height: "120px",
        background: `linear-gradient(135deg, ${CHART_COLORS.COURS}20, ${CHART_COLORS.ABONNEMENTS}20)`,
        borderRadius: "50%",
        zIndex: 0
      }}></div>

      <div style={{ position: "relative", zIndex: 1 }}>
        {/* En-tête du graphique */}
        <div style={{
          textAlign: "center",
          marginBottom: "25px",
          padding: "15px",
          backgroundColor: CHART_COLORS.LIGHT_BG,
          borderRadius: "10px",
          border: `2px solid ${CHART_COLORS.COURS}`
        }}>
          <h5 style={{
            color: CHART_COLORS.COURS,
            marginBottom: "8px",
            fontWeight: "700",
            fontSize: "20px",
            textShadow: "0 1px 2px rgba(0,0,0,0.1)"
          }}>
            🥧 Répartition des Entités du Système
          </h5>
          <p style={{
            color: CHART_COLORS.TEXT_DARK,
            margin: "0",
            fontSize: "14px",
            fontStyle: "italic"
          }}>
            Vue d'ensemble de la distribution des ressources pédagogiques
          </p>
          {totalEntities > 0 && (
            <div style={{
              marginTop: "10px",
              fontSize: "16px",
              fontWeight: "600",
              color: CHART_COLORS.TEXT_DARK
            }}>
              Total: {totalEntities} entités
            </div>
          )}
        </div>

        {data.length > 0 ? (
          <>
            {/* Message informatif si toutes les valeurs sont à 0 */}
            {totalEntities === 0 && (
              <div className="alert alert-info" style={{
                backgroundColor: CHART_COLORS.LIGHT_BG,
                borderLeft: `4px solid ${CHART_COLORS.COURS}`,
                borderRadius: "0 4px 4px 0",
                marginBottom: "20px"
              }}>
                <i className="fa fa-info-circle me-2" style={{ color: CHART_COLORS.COURS }}></i>
                <strong>Information :</strong> Le système ne contient pas encore de données.
                <br />
                <small>
                  Commencez par ajouter des cours, matières, niveaux et abonnements
                  pour voir la répartition dans ce graphique.
                </small>
              </div>
            )}

            {/* Graphique en secteurs */}
            <ResponsiveContainer width="100%" height={400}>
              <PieChart>
                <Pie
                  data={data}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomLabel}
                  outerRadius={120}
                  innerRadius={40}
                  fill="#8884d8"
                  dataKey="value"
                  animationBegin={0}
                  animationDuration={1000}
                >
                  {data.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                      stroke={CHART_COLORS.BACKGROUND}
                      strokeWidth={3}
                    />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
                <Legend
                  verticalAlign="bottom"
                  height={36}
                  formatter={(value, entry) => (
                    <span style={{
                      color: CHART_COLORS.TEXT_DARK,
                      fontWeight: "500"
                    }}>
                      {entry.payload.icon} {value}
                    </span>
                  )}
                />
              </PieChart>
            </ResponsiveContainer>

            {/* Statistiques détaillées */}
            <div style={{
              marginTop: "20px",
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
              gap: "15px"
            }}>
              {data.map((item, index) => (
                <div
                  key={item.name}
                  style={{
                    padding: "15px",
                    backgroundColor: CHART_COLORS.LIGHT_BG,
                    borderRadius: "10px",
                    border: `2px solid ${COLORS[index % COLORS.length]}`,
                    textAlign: "center",
                    boxShadow: `0 2px 8px ${COLORS[index % COLORS.length]}30`
                  }}
                >
                  <div style={{
                    fontSize: "24px",
                    marginBottom: "5px"
                  }}>
                    {item.icon}
                  </div>
                  <div style={{
                    fontSize: "28px",
                    fontWeight: "bold",
                    color: COLORS[index % COLORS.length],
                    marginBottom: "5px"
                  }}>
                    {item.value}
                  </div>
                  <div style={{
                    fontSize: "14px",
                    fontWeight: "600",
                    color: CHART_COLORS.TEXT_DARK,
                    marginBottom: "3px"
                  }}>
                    {item.name}
                  </div>
                  <div style={{
                    fontSize: "12px",
                    color: CHART_COLORS.NEUTRAL,
                    fontStyle: "italic"
                  }}>
                    {item.description}
                  </div>
                  {totalEntities > 0 && (
                    <div style={{
                      fontSize: "11px",
                      color: CHART_COLORS.NEUTRAL,
                      marginTop: "5px",
                      fontWeight: "500"
                    }}>
                      {((item.value / totalEntities) * 100).toFixed(1)}% du total
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Résumé global */}
            <div style={{
              marginTop: "20px",
              padding: "20px",
              background: `linear-gradient(135deg, ${CHART_COLORS.LIGHT_BG}, ${CHART_COLORS.BACKGROUND})`,
              borderRadius: "12px",
              border: `1px solid ${CHART_COLORS.NEUTRAL}`,
              textAlign: "center"
            }}>
              <h6 style={{
                color: CHART_COLORS.TEXT_DARK,
                marginBottom: "15px",
                fontWeight: "600"
              }}>
                📈 Résumé du Système Éducatif
              </h6>
              <div style={{
                display: "flex",
                justifyContent: "space-around",
                flexWrap: "wrap",
                gap: "10px"
              }}>
                <div style={{ textAlign: "center" }}>
                  <div style={{
                    fontSize: "20px",
                    fontWeight: "bold",
                    color: CHART_COLORS.COURS
                  }}>
                    {data.length}
                  </div>
                  <small style={{ color: CHART_COLORS.TEXT_DARK }}>
                    Types d'entités
                  </small>
                </div>
                <div style={{ textAlign: "center" }}>
                  <div style={{
                    fontSize: "20px",
                    fontWeight: "bold",
                    color: CHART_COLORS.NIVEAUX
                  }}>
                    {totalEntities}
                  </div>
                  <small style={{ color: CHART_COLORS.TEXT_DARK }}>
                    Total entités
                  </small>
                </div>
                <div style={{ textAlign: "center" }}>
                  <div style={{
                    fontSize: "20px",
                    fontWeight: "bold",
                    color: CHART_COLORS.ABONNEMENTS
                  }}>
                    {data.length > 0 ? Math.round(totalEntities / data.length) : 0}
                  </div>
                  <small style={{ color: CHART_COLORS.TEXT_DARK }}>
                    Moyenne/type
                  </small>
                </div>
              </div>
            </div>
          </>
        ) : (
          <div style={{
            textAlign: "center",
            padding: "50px",
            color: CHART_COLORS.NEUTRAL,
            backgroundColor: CHART_COLORS.LIGHT_BG,
            borderRadius: "12px",
            border: `2px dashed ${CHART_COLORS.NEUTRAL}`
          }}>
            <div style={{
              fontSize: "64px",
              marginBottom: "20px",
              color: CHART_COLORS.COURS,
              opacity: 0.6
            }}>
              🥧
            </div>
            <h6 style={{
              margin: "0 0 10px 0",
              fontSize: "18px",
              color: CHART_COLORS.TEXT_DARK,
              fontWeight: "600"
            }}>
              Aucune donnée disponible
            </h6>
            <p style={{
              margin: "0",
              color: CHART_COLORS.NEUTRAL,
              fontSize: "14px"
            }}>
              Les statistiques du système ne sont pas encore disponibles.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Export du composant
 *
 * Utilisation recommandée :
 * ```jsx
 * import StatisticsPieChart from './StatisticsPieChart';
 *
 * function Dashboard() {
 *   return (
 *     <div>
 *       <StatisticsPieChart />
 *     </div>
 *   );
 * }
 * ```
 *
 * Prérequis :
 * - Service axiosInstance configuré
 * - Endpoint API disponible : /api/statistics
 * - Bibliothèques recharts et react installées
 * - Icônes FontAwesome disponibles
 *
 * Personnalisation :
 * - Modifier CHART_COLORS pour changer la palette
 * - Ajuster les dimensions via ResponsiveContainer
 * - Personnaliser les tooltips et labels selon les besoins
 * - Modifier les icônes et descriptions des entités
 */
export default StatisticsPieChart;