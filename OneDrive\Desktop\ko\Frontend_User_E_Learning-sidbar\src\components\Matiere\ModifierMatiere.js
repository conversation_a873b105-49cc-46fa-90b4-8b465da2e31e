import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";

const API_MATIERES = "http://localhost:8084/api/matieres";

const ModifierMatiere = () => {
  const navigate = useNavigate();
  const { id } = useParams(); // Récupération de l'ID depuis l'URL

  const [formData, setFormData] = useState({
    nom: "",
    description: "",
    duree: "",
    nomDeProf: "",
    niveau: ""
  });

  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState("");

  // Charger les données de la matière
  useEffect(() => {
    fetch(`${API_MATIERES}/${id}`)
      .then((response) => response.json())
      .then((data) => setFormData(data))
      .catch((error) => console.error("Erreur :", error));
  }, [id]);

  // Gestion des changements dans les champs du formulaire
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value
    }));
    setErrors((prevErrors) => ({
      ...prevErrors,
      [name]: ""
    }));
  };

  // Validation du formulaire
  const validateForm = () => {
    let newErrors = {};

    if (!formData.nom.trim()) newErrors.nom = "Le nom est requis";
    if (!formData.description.trim()) newErrors.description = "La description est requise";
    if (!formData.duree.trim() || isNaN(Number(formData.duree))) newErrors.duree = "Durée invalide";
    if (!formData.nomDeProf.trim()) newErrors.nomDeProf = "Le professeur est requis";
    if (!formData.niveau.trim()) newErrors.niveau = "Le niveau est requis";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Envoi du formulaire
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    try {
      const response = await fetch(`${API_MATIERES}/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json"
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();
      setSuccessMessage("Matière modifiée avec succès!");
      setTimeout(() => {
        navigate("/AfficherMatiere");
      }, 2000);
    } catch (error) {
      console.error("Error:", error);
      setErrors({ general: `Erreur lors de la modification: ${error.message}` });
    }
  };

  return (
    <div className="content-body">
      <div className="container-fluid">
        <h4>Modifier une Matière</h4>

        {successMessage && <div className="alert alert-success">{successMessage}</div>}

        <form onSubmit={handleSubmit}>
          <div className="mb-3">
            <label className="form-label">Nom</label>
            <input name="nom" type="text" className="form-control" value={formData.nom} onChange={handleChange} />
            {errors.nom && <small className="text-danger">{errors.nom}</small>}
          </div>

          <div className="mb-3">
            <label className="form-label">Description</label>
            <textarea name="description" className="form-control" rows="3" value={formData.description} onChange={handleChange}></textarea>
            {errors.description && <small className="text-danger">{errors.description}</small>}
          </div>

          <div className="mb-3">
            <label className="form-label">Durée (Heures)</label>
            <input name="duree" type="number" className="form-control" value={formData.duree} onChange={handleChange} />
            {errors.duree && <small className="text-danger">{errors.duree}</small>}
          </div>

          <button type="submit" className="btn btn-primary me-2">Modifier</button>
          <button onClick={() => navigate("/AfficherMatiere")} className="btn btn-secondary">Annuler</button>
        </form>
      </div>
    </div>
  );
};

export default ModifierMatiere;
