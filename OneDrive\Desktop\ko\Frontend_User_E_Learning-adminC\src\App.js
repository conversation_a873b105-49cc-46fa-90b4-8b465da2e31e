import React, { Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';
import './styles/global.css';
import './styles/responsive.css';
import './styles/theme.css';
import PrivateRoute from './components/PrivateRoute';
import Login from './components/Login/login';
import RedirectToDashboard from './components/RedirectToDashboard';
import Dashboard from './components/dashboard/dashboard';
import AfficherMatiere from './components/Matiere/AfficherMatiere';
import AjouterMatiere from './components/Matiere/AjouterMatiere';
import AfficherChapitre from './components/Chapitre/AfficherChapitre';
import AjouterChapitre from './components/Chapitre/AjouterChapitre';
import AfficherCours from './components/cours/AfficheCours';
import Profile from './components/profile/Profile';
import EditProfile from './components/profile/EditProfile';
import AjoutCoursParMatiere from './components/cours/AjoutCoursParMatiere';
import AfficheCoursParChapitre from './components/cours/AfficheCoursParChapitre';
import AfficherEnseignant from './components/enseignant/AfficherEnseignant';
import AjouterEnseignant from './components/enseignant/AjouterEnseignant';
import AfficherEtudiant from './components/etudiant/AfficherEtudiant';
import AjoutEtud from './components/etudiant/AjoutEtud';
import AfficherNiveau from './components/NiveauEtude/AfficherNiveau';
import AjouterNiveau from './components/NiveauEtude/AjouterNiveau';
import AfficherTypeAbonnement from './components/TypeAbonnements/AfficherTypeAbonnement';
import AjouterTypeAbonnement from './components/TypeAbonnements/AjouterTypeAbonnement';
import AfficherAbonnement from './components/Abonnement/AfficherAbonnement';
import AjoutAbonnement from './components/Abonnement/AjoutAbonnement';
import Affichage from './components/Abonnement/Affichage';
import ChapitreParMatiere from './components/Matiere/ChapitreParMatiere';
import MesMatieresEnseignant from './components/enseignant/MesMatieresEnseignant';
import MesNiveauxEnseignant from './components/enseignant/MesNiveauxEnseignant';
import Unauthorized from './components/Unauthorized'; // ou où tu l'as mis
import AjouterCours from "./components/cours/AjouterCours";
import AjoutChapParMatiere from "./components/Chapitre/AjoutChapParMatiere";
import MatiereEtudiant from './components/etudiant/MatiereEtudiant';
import AbonnementEtudiant from './components/etudiant/AbonnementEtudiant';
import LessonPage from './components/etudiant/LessonPage';
import PendingRegistrations from './components/etudiant/PendingRegistrations';
import RecordingsList from './components/etudiant/RecordingsList';
import TextToVoice from './components/etudiant/TextToVoice';
import ChatModel from './components/etudiant/ChatModel';

// Import LiveSession components
import LiveSession from './LiveSession/LiveSession';
import CreateLiveSession from './LiveSession/CreateLiveSession';
import EditLiveSession from './LiveSession/EditLiveSession';
import AvailableSession from './LiveSession/AvailableSession';
import JoinLiveSession from './LiveSession/JoinLiveSession';

// Import Forum components
import ForumPage from './components/Forum/ForumPage';
import PoserQuestion from './components/Forum/PoserQuestion';
import QuestionDetail from './components/Forum/QuestionDetail';



function App() {
  return (
    <Routes>
      <Route path="/" element={<RedirectToDashboard />} />
      <Route path="/login" element={<Login />} />
      <Route path="/dashboard" element={<PrivateRoute><Dashboard /></PrivateRoute>} />
      <Route path="/profile" element={<PrivateRoute><Profile /></PrivateRoute>} />
      <Route path="/profile/edit" element={<PrivateRoute><EditProfile /></PrivateRoute>} />

      {/* Matières routes */}
      <Route path="/matieres" element={<PrivateRoute><AfficherMatiere /></PrivateRoute>} />
      <Route path="/matieres/ajouter" element={<PrivateRoute><AjouterMatiere /></PrivateRoute>} />
      <Route path="/mes-matieres" element={<PrivateRoute><MesMatieresEnseignant /></PrivateRoute>} />
      <Route path="/mes-niveaux" element={<PrivateRoute><MesNiveauxEnseignant /></PrivateRoute>} />
      <Route path="/Mes-matieresETUD" element={<PrivateRoute><MatiereEtudiant /></PrivateRoute>} />


      {/* Chapitres routes */}
      <Route path="/chapitres" element={<PrivateRoute><AfficherChapitre /></PrivateRoute>} />
      <Route path="/chapitres/ajouter" element={<PrivateRoute><AjouterChapitre /></PrivateRoute>} />
      <Route path="/AjoutChapParMatiere/:idMatiere/" element={<PrivateRoute><AjoutChapParMatiere /></PrivateRoute>} />

      <Route path="/chapitres/matiere/:idMatiere/:idNiveau" element={<PrivateRoute><ChapitreParMatiere /></PrivateRoute>} />
      <Route path="/chapitres/matiere/:idMatiere" element={<PrivateRoute><ChapitreParMatiere /></PrivateRoute>} />

      {/* Cours routes */}
      <Route path="/cours" element={<PrivateRoute><AfficherCours /></PrivateRoute>} />
      {/*<Route path="/cours/ajouter" element={<PrivateRoute><AjoutCoursParMatiere /></PrivateRoute>} />*/}
      <Route path="/cours/chapitre/:id" element={<PrivateRoute><AfficheCoursParChapitre /></PrivateRoute>} />
      <Route path="AjouterCours" element={<PrivateRoute><AjouterCours /></PrivateRoute>} />
      <Route path="/lesson/:chapitreId/:coursId?" element={<PrivateRoute><LessonPage /></PrivateRoute>} />

      <Route path="/AjoutCoursParMatiere/:idMatiere/:id" element={<PrivateRoute><AjoutCoursParMatiere /></PrivateRoute>} />

      {/* Enseignant routes */}
      <Route path="/enseignants" element={<PrivateRoute><AfficherEnseignant /></PrivateRoute>} />
      <Route path="/ajouter-enseignant" element={<PrivateRoute><AjouterEnseignant /></PrivateRoute>} />
      <Route path="/enseignants/:id" element={<PrivateRoute><AfficherEnseignant /></PrivateRoute>} />

      {/* Etudiant routes */}
      <Route path="/etudiants" element={<PrivateRoute><AfficherEtudiant /></PrivateRoute>} />
      <Route path="/afficher-etudiant" element={<PrivateRoute><AfficherEtudiant /></PrivateRoute>} />
      <Route path="/ajouter-etudiant" element={<PrivateRoute><AjoutEtud /></PrivateRoute>} />

      {/* Niveau routes */}
      <Route path="/niveaux-etude" element={<PrivateRoute><AfficherNiveau /></PrivateRoute>} />
      <Route path="/niveaux-etude/ajouter" element={<PrivateRoute><AjouterNiveau /></PrivateRoute>} />

      <Route path="/pending-registrations" element={<PrivateRoute><PendingRegistrations /></PrivateRoute>} />

      {/* Abonnement routes */}
      <Route path="/type-abonnements" element={<PrivateRoute><AfficherTypeAbonnement /></PrivateRoute>} />
      <Route path="/ajouter-type-abonnement" element={<PrivateRoute><AjouterTypeAbonnement /></PrivateRoute>} />
      <Route path="/abonnements" element={<PrivateRoute><AfficherAbonnement /></PrivateRoute>} />
      <Route path="/abonnements/ajouter" element={<PrivateRoute><AjoutAbonnement /></PrivateRoute>} />
      <Route path="/affichage" element={<PrivateRoute><Affichage /></PrivateRoute>} />
      <Route path="/unauthorized" element={<Unauthorized />} />
      <Route path='/Mes-abonnement' element={<PrivateRoute> <AbonnementEtudiant/></PrivateRoute>}/>

      {/* Live Session routes */}
      <Route path="/live-sessions" element={<PrivateRoute><LiveSession /></PrivateRoute>} />
      <Route path="/live-sessions/create" element={<PrivateRoute><CreateLiveSession /></PrivateRoute>} />
      <Route path="/live-sessions/edit/:sessionId" element={<PrivateRoute><EditLiveSession /></PrivateRoute>} />
      <Route path="/live-sessions/available" element={<PrivateRoute><AvailableSession /></PrivateRoute>} />
      <Route path="/live-sessions/join" element={<PrivateRoute><AvailableSession /></PrivateRoute>} />
      <Route path="/live-sessions/join/:sessionId" element={<PrivateRoute><JoinLiveSession /></PrivateRoute>} />

      {/* Recordings routes */}
      <Route path="/recordings" element={<PrivateRoute><RecordingsList /></PrivateRoute>} />

      {/* Text to Voice routes */}
      <Route path="/text-to-voice" element={<PrivateRoute><TextToVoice /></PrivateRoute>} />

      {/* Chat Model routes */}
      <Route path="/chat-model" element={<PrivateRoute><ChatModel /></PrivateRoute>} />

      {/* Forum routes */}
      <Route path="/forum" element={<PrivateRoute><ForumPage /></PrivateRoute>} />
      <Route path="/forum/poser-question" element={<PrivateRoute><PoserQuestion /></PrivateRoute>} />
      <Route path="/forum/question/:id" element={<PrivateRoute><QuestionDetail /></PrivateRoute>} />
      <Route path="/forum/mes-questions" element={<PrivateRoute><ForumPage /></PrivateRoute>} />
    </Routes>
  );
}

export default App;