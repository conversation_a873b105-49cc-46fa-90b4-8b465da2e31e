import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Modal, Button, Form, Pagination } from "react-bootstrap";
import axiosInstance from "../../services/axiosService";
import axios from "axios";
import { useKeycloak } from "@react-keycloak/web";

const ChapitreParMatiere = () => {
  const navigate = useNavigate();
  const { idMatiere, idNiveau } = useParams();
  const { keycloak } = useKeycloak();

  const [chapitres, setChapitres] = useState([]);
  const [chapitreModif, setChapitreModif] = useState(null);
  const [chapitreSuppr, setChapitreSuppr] = useState(null);
  const [nouveauNom, setNouveauNom] = useState("");
  const [nouveauProf, setNouveauProf] = useState("");
  const [nouvelleDuree, setNouvelleDuree] = useState(0);
  const [nouveauNombreDeCours, setNouveauNombreDeCours] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [selectedChapitre, setSelectedChapitre] = useState(null);
  const [nombreDeCours, setNombreDeCours] = useState({});
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [niveaux, setNiveaux] = useState([]);
  const [showNiveaux, setShowNiveaux] = useState(false);
  const [userRole, setUserRole] = useState("");
  const [userId, setUserId] = useState(null);
  const [userNiveauId, setUserNiveauId] = useState(null);

  // Fonction pour charger les chapitres
  // Fonction pour charger les chapitres
  const itemsPerPage = 8;

  const fetchChapitres = async () => {
    setLoading(true);
    try {
      let url;
      if (idNiveau) {
        // If idNiveau is provided, use the specific endpoint
        url = `/api/chapitres/matiere/${idMatiere}/niveau/${idNiveau}`;
      } else {
        // If idNiveau is not provided, use the general endpoint
        url = `/api/chapitres/matiere/${idMatiere}`;
      }

      const response = await axiosInstance.get(url);

      // Modification ici - utilisez response.data si l'API retourne directement le tableau
      const chapitresData = response.data.content || response.data;
      setChapitres(chapitresData);

      // Si votre API retourne des infos de pagination
      if (response.data.totalPages !== undefined) {
        setTotalPages(response.data.totalPages);
        setTotalElements(response.data.totalElements);
      } else {
        // If pagination info is not available, calculate it based on the array length
        const totalItems = chapitresData.length;
        const calculatedTotalPages = Math.ceil(totalItems / itemsPerPage);
        setTotalPages(calculatedTotalPages);
        setTotalElements(totalItems);
      }

      // Charger le nombre de cours pour chaque chapitre
      if (chapitresData && chapitresData.length > 0) {
        chapitresData.forEach((chapitre) => {
          axiosInstance
          .get(`/api/cours/chapitre/${chapitre.id}/count`)
          .then((countResponse) => {
            setNombreDeCours((prev) => ({
              ...prev,
              [chapitre.id]: countResponse.data.count,
            }));
          })
          .catch((error) => {
            console.error("Erreur :", error);
          });
        });
      }
    } catch (err) {
      console.error("Erreur lors du chargement des chapitres :", err);
      setError("Erreur lors du chargement des chapitres");
      setChapitres([]); // Initialiser à un tableau vide en cas d'erreur
    } finally {
      setLoading(false);
    }
  };
  // Déterminer le rôle de l'utilisateur et récupérer son niveau si c'est un étudiant
  useEffect(() => {
    if (keycloak.authenticated) {
      // Récupérer tous les rôles disponibles
      const roles = keycloak.tokenParsed?.realm_access?.roles || [];
      console.log("Tous les rôles disponibles:", roles);

      // Vérifier les rôles de manière insensible à la casse
      const hasRole = (role) => {
        return roles.some(r => r.toLowerCase() === role.toLowerCase());
      };

      const isAdmin = hasRole("admin");
      const isEnseignant = hasRole("enseignant");
      const isEtudiant = hasRole("etudiant");

      // Afficher les rôles pour le débogage
      console.log("isAdmin:", isAdmin);
      console.log("isEnseignant:", isEnseignant);
      console.log("isEtudiant:", isEtudiant);

      // Définir le rôle de l'utilisateur
      if (isAdmin) {
        setUserRole("ADMIN");
        console.log("Rôle défini: ADMIN");
      } else if (isEnseignant) {
        setUserRole("ENSEIGNANT");
        console.log("Rôle défini: ENSEIGNANT");
        // Pour les enseignants, on pourrait récupérer leurs niveaux associés ici
      } else if (isEtudiant) {
        setUserRole("ETUDIANT");
        console.log("Rôle défini: ETUDIANT");

        // Si c'est un étudiant, récupérer son ID et son niveau
        const fetchEtudiantInfo = async () => {
          try {
            // Récupérer directement les chapitres pour cette matière
            if (!idNiveau) {
              // Si on n'a pas de niveau dans l'URL, on récupère tous les chapitres pour cette matière
              try {
                const chapitresResponse = await axiosInstance.get(`/api/chapitres/matiere/${idMatiere}`);
                if (chapitresResponse.data && chapitresResponse.data.length > 0) {
                  // Si on a des chapitres, on les affiche
                  setChapitres(chapitresResponse.data);
                }
              } catch (chapitresErr) {
                console.error("Erreur lors de la récupération des chapitres:", chapitresErr);
              }
            }
          } catch (err) {
            console.error("Erreur lors de la récupération des informations de l'étudiant:", err);
          }
        };

        fetchEtudiantInfo();
      }
    }
  }, [keycloak, keycloak.authenticated, idMatiere, idNiveau, navigate]);

  useEffect(() => {
    if (searchTerm) {
      // Mode recherche
      axiosInstance.get(`/api/chapitres/search?nomChapitre=${searchTerm}`)
        .then(response => {
          setChapitres(response.data);
          setTotalPages(1);
          setTotalElements(response.data.length);
        })
        .catch(err => setError(err.message));
    } else {
      // Mode pagination normale
      fetchChapitres();
    }
  }, [currentPage, searchTerm, idMatiere, idNiveau]);
  const indexOfLastChapitre = currentPage * itemsPerPage;
  const indexOfFirstChapitre = indexOfLastChapitre - itemsPerPage;
  const chapitresAffiches = chapitres.slice(
    indexOfFirstChapitre,
    indexOfLastChapitre
  );
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const supprimerChapitre = async () => {
    try {
      await axiosInstance.delete(`/api/chapitres/${chapitreSuppr.id}`);
      setChapitres(chapitres.filter((chap) => chap.id !== chapitreSuppr.id));
      setChapitreSuppr(null);
    } catch (err) {
      setError(err.message);
    }
  };

  const modifierChapitre = async () => {
    try {
      await axiosInstance.put(`/api/chapitres/${chapitreModif.id}`, {
        nomChapitre: nouveauNom,
        nomDeProf: nouveauProf,
        duree: nouvelleDuree,
        nombreDeCours: nouveauNombreDeCours,
      });

      setChapitres(
        chapitres.map((chap) =>
          chap.id === chapitreModif.id
            ? {
                ...chap,
                nomChapitre: nouveauNom,
                nomDeProf: nouveauProf,
                duree: nouvelleDuree,
                nombreDeCours: nouveauNombreDeCours,
              }
            : chap
        )
      );
      setChapitreModif(null);
    } catch (err) {
      setError(err.message);
    }
  };

  const ouvrirFormModif = (chapitre) => {
    setChapitreModif(chapitre);
    setNouveauNom(chapitre.nomChapitre);
    setNouveauProf(chapitre.nomDeProf);
    setNouvelleDuree(chapitre.duree);
    setNouveauNombreDeCours(chapitre.nombreDeCours);
  };


  // Fonction pour rendre la pagination
    const renderPagination = () => {
      let items = [];
      for (let number = 0; number < totalPages; number++) {
        items.push(
          <Pagination.Item
            key={number}
            active={number === currentPage}
            onClick={() => handlePageChange(number)}
          >
            {number + 1}
          </Pagination.Item>
        );
      }
     return (
          <div className="d-flex justify-content-center mt-4">
            <Pagination>
              <Pagination.First onClick={() => handlePageChange(0)} disabled={currentPage === 0} />
              <Pagination.Prev onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage === 0} />
              {items}
              <Pagination.Next onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage === totalPages - 1} />
              <Pagination.Last onClick={() => handlePageChange(totalPages - 1)} disabled={currentPage === totalPages - 1} />
            </Pagination>
          </div>
        );
      };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };
  const handleListeClick = async () => {
    try {
      const response = await axiosInstance.get("/api/niveaux/all");
      setNiveaux(response.data);
      setShowNiveaux(true);
    } catch (error) {
      console.error("Erreur en récupérant les niveaux", error);
      setError("Erreur lors du chargement des niveaux d'étude");
    }
  };

  const handleNiveauSelect = async (e) => {
    const niveauId = e.target.value;
    if (!niveauId) return;

    try {
      // Navigate to the route with both matiere and niveau IDs
      navigate(`/chapitres/matiere/${idMatiere}/${niveauId}`);
    } catch (error) {
      console.error("Erreur lors de la navigation", error);
      setError("Erreur lors de la navigation vers le niveau sélectionné");
    }
  };
  return (
    <div className="container-fluid">
      <div className="row page-titles mx-0 d-flex align-items-center justify-content-between">
        <div className="col-auto">
          <h4 style={{ color: "#37A7DF" }}>Tous Les Chapitres</h4>
        </div>
        {/* Champ de recherche */}
                 <div className="col-md-4">
                   <Form.Control
                     type="text"
                     placeholder="Rechercher un chapitre..."
                     value={searchTerm}
                     onChange={(e) => {
                       setSearchTerm(e.target.value);
                       setCurrentPage(0); // Réinitialiser à la première page lors d'une nouvelle recherche
                     }}
                   />
                 </div>

        {/* Bouton Ajouter - visible uniquement pour admin et enseignant */}
        {(userRole === "ADMIN" || userRole === "ENSEIGNANT") && (
          <div className="col-auto">
            <button
              className="btn btn-primary"
              style={{
                backgroundColor: "#37A7DF",
                borderColor: "#37A7DF",
                color: "#fff",
              }}
              onClick={() => navigate(`/AjoutChapParMatiere/${idMatiere}`)}
            >
              + Ajouter un Chapitre
            </button>
          </div>
        )}
      </div>

      {error && <div className="alert alert-danger">{error}</div>}
      {loading ? (
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="sr-only">Chargement...</span>
          </div>
        </div>
      ) : (
        <div className="row g-3">
          {chapitres.length > 0 ? (
            <>
              {chapitres.map((chapitre) => (
                <div className="col-lg-3 col-md-4 col-sm-6" key={chapitre.id}>
                  <div className="card shadow-sm border-0 rounded-lg">
                    <div className="card-body text-center p-2">
                      <h6 className="fw-bold">Titre du chapitre :</h6>
                      <h4 key={chapitre.idChapitre}>{chapitre.nomChapitre}</h4>

                      <p>
                        <strong>Professeur :</strong> {chapitre.nomDeProf}
                      </p>
                      <p>
                        <strong>Durée :</strong> {chapitre.duree} heures
                      </p>
                      <p>
                        <strong>Nombre de cours :</strong>{" "}
                        {nombreDeCours[chapitre.id] || 0}{" "}
                        {/* Afficher le nombre de cours */}
                      </p>

                      {/* Bouton pour sélectionner un chapitre
                      <button
                        onClick={() => setSelectedChapitre(chapitre)}
                        className="btn mt-2 w-100 btn-sm"
                        style={{
                          backgroundColor:
                            selectedChapitre?.id === chapitre.id
                              ? "#F2BC00"
                              : "#37A7DF",
                          color: "white",
                        }}
                      >
                        {selectedChapitre?.id === chapitre.id
                          ? "Sélectionné"
                          : "Sélectionner ce chapitre"}
                      </button>*/}
                      {/* Bouton Ajouter un cours - visible uniquement pour admin et enseignant */}
                      {(userRole === "ADMIN" || userRole === "ENSEIGNANT") && (
                        <button
                          className="btn mt-2 w-100 btn-sm"
                          style={{
                            backgroundColor: "#37A7DF",
                            borderColor: "#37A7DF",
                            color: "#fff",
                          }}
                          onClick={() =>
                            navigate(
                              `/AjoutCoursParMatiere/${idMatiere}/${chapitre.id}`
                            )
                          }
                        >
                          + Ajouter un cours
                        </button>
                      )}

                      {/* Liste des cours - visible pour tous */}
                      <button
                        onClick={() => navigate(`/cours/chapitre/${chapitre.id}`)}
                        className="btn mt-2 w-100 btn-sm"
                        style={{
                          backgroundColor: "#B7B7B7",
                          borderColor: "#B7B7B7",
                          color: "#fff",
                        }}
                      >
                        Liste des cours
                      </button>

                      {/* Voir les leçons - visible pour tous */}
                      <button
                        onClick={() => navigate(`/lesson/${chapitre.id}`)}
                        className="btn mt-2 w-100 btn-sm"
                        style={{
                          backgroundColor: "#248E39",
                          borderColor: "#248E39",
                          color: "#fff",
                        }}
                      >
                        Voir les leçons
                      </button>

                      {/* Bouton Modifier - visible uniquement pour admin et enseignant */}
                      {(userRole === "ADMIN" || userRole === "ENSEIGNANT") && (
                        <button
                          className="btn mt-2 w-100 btn-sm"
                          onClick={() => ouvrirFormModif(chapitre)}
                          style={{ backgroundColor: "#F2BC00", color: "white" }}
                        >
                          Modifier
                        </button>
                      )}

                      {/* Bouton Supprimer - visible uniquement pour admin et enseignant */}
                      {(userRole === "ADMIN" || userRole === "ENSEIGNANT") && (
                        <button
                          className="btn btn-danger light mt-2 w-100 btn-sm"
                          onClick={() => setChapitreSuppr(chapitre)}
                        >
                          Supprimer
                        </button>
                      )}
                    </div>


                  </div>
                </div>
              ))}
            </>
          ) : (
            <div className="col-12 text-center py-5">
              <div className="alert alert-info">
                {idNiveau ?
                  "Aucun chapitre disponible pour cette matière et ce niveau." :
                  (userRole === "ADMIN" ?
                    "Pour voir les chapitres spécifiques à un niveau d'étude, veuillez sélectionner un niveau." :
                    "Aucun chapitre disponible pour cette matière."
                  )
                }
              </div>
              {!idNiveau && userRole === "ADMIN" && (
                <div className="mt-4">
                  <button
                    className="btn btn-primary"
                    onClick={handleListeClick}
                    style={{
                      backgroundColor: "#37A7DF",
                      borderColor: "#37A7DF",
                    }}
                  >
                    Sélectionner un niveau d'étude
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      )}
         {/* Pagination : boutons pour naviguer entre les pages */}
         <div className="d-flex justify-content-center mt-3">
          <button
            className="btn btn-secondary mx-1"
            onClick={goToPrevPage}
            disabled={currentPage === 1}
          >
            Précédent
          </button>
          <span className="mx-2">
            Page {currentPage} sur {totalPages}
          </span>
          <button
            className="btn btn-secondary mx-1"
            onClick={goToNextPage}
            disabled={currentPage === totalPages}
          >
            Suivant
          </button>
        </div>
      {/* Modal de Modification */}
      {chapitreModif && (
        <div
          className="modal fade show d-block"
          style={{ background: "rgba(0,0,0,0.5)" }}
        >
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Modifier le chapitre</h5>
                <button
                  className="btn-close"
                  onClick={() => setChapitreModif(null)}
                ></button>
              </div>
              <div className="modal-body">
                <p>Nom du chapitre</p>
                <input
                  type="text"
                  className="form-control mb-2"
                  value={nouveauNom}
                  onChange={(e) => setNouveauNom(e.target.value)}
                  placeholder="Nom du chapitre"
                />
                <p>Professeur</p>

                <input
                  type="text"
                  className="form-control mb-2"
                  value={nouveauProf}
                  onChange={(e) => setNouveauProf(e.target.value)}
                  placeholder="Professeur"
                />
                <p>Durée</p>

                <input
                  type="number"
                  className="form-control mb-2"
                  value={nouvelleDuree}
                  onChange={(e) => setNouvelleDuree(e.target.value)}
                  placeholder="Durée"
                />
                <p>Nombre de cours</p>
                <input
                  type="number"
                  className="form-control mb-2"
                  value={nouveauNombreDeCours}
                  onChange={(e) => setNouveauNombreDeCours(e.target.value)}
                  placeholder="Nombre de cours"
                />
              </div>
              <div className="modal-footer">
                <button
                  className="btn btn-danger light"
                  onClick={() => setChapitreModif(null)}
                >
                  Annuler ChatGPT a dit : php-template Copier Modifier
                </button>
                <button className="btn btn-primary" onClick={modifierChapitre}>
                  Enregistrer
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de suppression */}
      {chapitreSuppr && (
        <div
          className="modal fade show d-block"
          style={{ background: "rgba(0,0,0,0.5)" }}
        >
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Confirmation</h5>
                <button
                  className="btn-close"
                  onClick={() => setChapitreSuppr(null)}
                ></button>
              </div>
              <div className="modal-body">
                Êtes-vous sûr de vouloir supprimer ce chapitre ?
              </div>
              <div className="modal-footer">
                <button
                  className="btn btn-secondary"
                  onClick={() => setChapitreSuppr(null)}
                >
                  Annuler
                </button>
                <button className="btn btn-danger" onClick={supprimerChapitre}>
                  Supprimer
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de sélection de niveau */}
      {showNiveaux && (
        <div
          className="modal fade show d-block"
          style={{ background: "rgba(0,0,0,0.5)" }}
        >
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Sélectionner un niveau d'étude</h5>
                <button
                  className="btn-close"
                  onClick={() => setShowNiveaux(false)}
                ></button>
              </div>
              <div className="modal-body">
                <Form.Group>
                  <Form.Label>Niveau d'étude</Form.Label>
                  <Form.Control
                    as="select"
                    onChange={(e) => {
                      const niveauId = e.target.value;
                      if (niveauId) {
                        navigate(`/chapitres/matiere/${idMatiere}/${niveauId}`);
                        setShowNiveaux(false);
                      }
                    }}
                  >
                    <option value="">Sélectionner un niveau</option>
                    {niveaux.map((niveau) => (
                      <option key={niveau.id} value={niveau.id}>
                        {niveau.nom || niveau.nomNiveau}
                      </option>
                    ))}
                  </Form.Control>
                </Form.Group>
              </div>
              <div className="modal-footer">
                <button
                  className="btn btn-secondary"
                  onClick={() => setShowNiveaux(false)}
                >
                  Annuler
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChapitreParMatiere;
