import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useKeycloak } from '@react-keycloak/web';
import imageCours from "../../images/deutza noir.png";
import abonnementTypeService from '../../services/abonnementTypeService';
import { <PERSON>dal, Button, Spinner, Alert } from 'react-bootstrap';
import './TypeAbonnement.css';

const AfficherTypeAbonnement = () => {
    const [types, setTypes] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [typeModif, setTypeModif] = useState(null);
    const [nouveauNom, setNouveauNom] = useState("");
    const [nouvelleDescription, setNouvelleDescription] = useState("");
    const [hasCourses, setHasCourses] = useState(true);
    const [hasRecordings, setHasRecordings] = useState(false);
    const [hasLiveSessions, setHasLiveSessions] = useState(false);
    const [showInitModal, setShowInitModal] = useState(false);
    const [initializing, setInitializing] = useState(false);
    const [initSuccess, setInitSuccess] = useState(false);

    const { keycloak } = useKeycloak();
    const navigate = useNavigate();

    // Fetch subscription types on component mount
    useEffect(() => {
        fetchTypes();
    }, []);

    const fetchTypes = async () => {
        try {
            setLoading(true);
            setError(null);
            const data = await abonnementTypeService.getAllTypes();
            setTypes(data);
        } catch (err) {
            console.error("Error fetching subscription types:", err);
            setError("Erreur lors du chargement des types d'abonnement");
        } finally {
            setLoading(false);
        }
    };

    // Function to delete a subscription type
    const supprimerType = async (id) => {
        const confirmation = window.confirm(
            "Voulez-vous vraiment supprimer ce type d'abonnement ?"
        );

        if (confirmation) {
            try {
                await abonnementTypeService.deleteType(id);
                setTypes(types.filter((type) => type.id !== id));
            } catch (err) {
                console.error("Error deleting subscription type:", err);
                alert("Erreur lors de la suppression du type d'abonnement");
            }
        }
    };

    // Function to open the edit form
    const ouvrirFormModif = (type) => {
        setTypeModif(type);
        setNouveauNom(type.nom);
        setNouvelleDescription(type.description || "");
        setHasCourses(type.hasCourses);
        setHasRecordings(type.hasRecordings);
        setHasLiveSessions(type.hasLiveSessions);
    };

    // Function to update a subscription type
    const modifierType = async () => {
        try {
            const updatedType = {
                id: typeModif.id,
                nom: nouveauNom,
                description: nouvelleDescription,
                hasCourses: hasCourses,
                hasRecordings: hasRecordings,
                hasLiveSessions: hasLiveSessions
            };

            await abonnementTypeService.updateType(typeModif.id, updatedType);

            // Update the local state
            setTypes(
                types.map((type) =>
                    type.id === typeModif.id ? updatedType : type
                )
            );

            // Close the form
            setTypeModif(null);
        } catch (err) {
            console.error("Error updating subscription type:", err);
            alert("Erreur lors de la mise à jour du type d'abonnement");
        }
    };

    // Function to initialize default subscription types
    const initializeDefaultTypes = async () => {
        try {
            setInitializing(true);
            await abonnementTypeService.initializeDefaultTypes();
            setInitSuccess(true);
            fetchTypes(); // Refresh the list
        } catch (err) {
            console.error("Error initializing default subscription types:", err);
            setError("Erreur lors de l'initialisation des types d'abonnement par défaut");
        } finally {
            setInitializing(false);
        }
    };
  return (
    
      <div className="container-fluid">
        <div className="row page-titles mx-0">
          <div className="col-sm-6 p-md-0">
            <div className="welcome-text">
              <h4>Types d'abonnements</h4>
            </div>
          </div>
          <div className="col-sm-6 p-md-0 justify-content-sm-end mt-2 mt-sm-0 d-flex">
            <button
              className="btn btn-success me-2"
              onClick={() => navigate('/ajouter-type-abonnement')}
            >
              + Ajouter un type
            </button>
            <button
              className="btn btn-info"
              onClick={() => setShowInitModal(true)}
            >
              Initialiser les types par défaut
            </button>
          </div>
        </div>

        {/* Error message */}
        {error && (
          <div className="alert alert-danger" role="alert">
            {error}
          </div>
        )}

        {/* Loading spinner */}
        {loading ? (
          <div className="text-center my-5">
            <Spinner animation="border" role="status">
              <span className="visually-hidden">Chargement...</span>
            </Spinner>
            <p className="mt-2">Chargement des types d'abonnement...</p>
          </div>
        ) : (
          <>
            {types.length === 0 ? (
              <div className="alert alert-info" role="alert">
                Aucun type d'abonnement trouvé. Vous pouvez en ajouter un nouveau ou initialiser les types par défaut.
              </div>
            ) : (
              <div className="row g-3">
                {types.map((type) => (
                  <div className="col-lg-4 col-md-6 col-sm-12" key={type.id}>
                    <div className="card shadow-sm border-0 rounded-lg overflow-hidden h-100">
                      <div className="card-header bg-primary text-white">
                        <h5 className="card-title mb-0">{type.nom}</h5>
                      </div>
                      <div className="card-body">
                        <p className="card-text">{type.description || "Aucune description"}</p>

                        <div className="features-list mb-3">
                          <div className={`feature-badge ${type.hasCourses ? 'bg-success' : 'bg-danger'} text-white mb-2 p-2 rounded`}>
                            <i className={`fa ${type.hasCourses ? 'fa-check' : 'fa-times'} me-2`}></i>
                            Cours: {type.hasCourses ? 'Oui' : 'Non'}
                          </div>
                          <div className={`feature-badge ${type.hasRecordings ? 'bg-success' : 'bg-danger'} text-white mb-2 p-2 rounded`}>
                            <i className={`fa ${type.hasRecordings ? 'fa-check' : 'fa-times'} me-2`}></i>
                            Enregistrements: {type.hasRecordings ? 'Oui' : 'Non'}
                          </div>
                          <div className={`feature-badge ${type.hasLiveSessions ? 'bg-success' : 'bg-danger'} text-white mb-2 p-2 rounded`}>
                            <i className={`fa ${type.hasLiveSessions ? 'fa-check' : 'fa-times'} me-2`}></i>
                            Sessions Live: {type.hasLiveSessions ? 'Oui' : 'Non'}
                          </div>
                        </div>
                      </div>
                      <div className="card-footer bg-light d-flex justify-content-between">
                        <button
                          className="btn btn-primary"
                          onClick={() => ouvrirFormModif(type)}
                        >
                          <i className="fa fa-edit me-1"></i> Modifier
                        </button>
                        <button
                          className="btn btn-danger"
                          onClick={() => supprimerType(type.id)}
                        >
                          <i className="fa fa-trash me-1"></i> Supprimer
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}

        {/* Edit Modal */}
        <Modal show={typeModif !== null} onHide={() => setTypeModif(null)}>
          <Modal.Header closeButton>
            <Modal.Title>Modifier le Type d'abonnement</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div className="mb-3">
              <label htmlFor="nom" className="form-label">Nom</label>
              <input
                type="text"
                className="form-control"
                id="nom"
                value={nouveauNom}
                onChange={(e) => setNouveauNom(e.target.value)}
              />
            </div>
            <div className="mb-3">
              <label htmlFor="description" className="form-label">Description</label>
              <textarea
                className="form-control"
                id="description"
                rows="3"
                value={nouvelleDescription}
                onChange={(e) => setNouvelleDescription(e.target.value)}
              ></textarea>
            </div>
            <div className="mb-3">
              <div className="form-check form-switch">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="hasCourses"
                  checked={hasCourses}
                  onChange={(e) => setHasCourses(e.target.checked)}
                />
                <label className="form-check-label" htmlFor="hasCourses">
                  Accès aux cours
                </label>
              </div>
              <div className="form-check form-switch">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="hasRecordings"
                  checked={hasRecordings}
                  onChange={(e) => setHasRecordings(e.target.checked)}
                />
                <label className="form-check-label" htmlFor="hasRecordings">
                  Accès aux enregistrements
                </label>
              </div>
              <div className="form-check form-switch">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="hasLiveSessions"
                  checked={hasLiveSessions}
                  onChange={(e) => setHasLiveSessions(e.target.checked)}
                />
                <label className="form-check-label" htmlFor="hasLiveSessions">
                  Accès aux sessions live
                </label>
              </div>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setTypeModif(null)}>
              Annuler
            </Button>
            <Button variant="primary" onClick={modifierType}>
              Enregistrer
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Initialize Default Types Modal */}
        <Modal show={showInitModal} onHide={() => setShowInitModal(false)}>
          <Modal.Header closeButton>
            <Modal.Title>Initialiser les types d'abonnement par défaut</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            {initSuccess ? (
              <Alert variant="success">
                Les types d'abonnement par défaut ont été initialisés avec succès !
              </Alert>
            ) : (
              <p>
                Cette action va créer les types d'abonnement par défaut suivants :
                <ul>
                  <li><strong>Basic</strong> : Accès aux cours uniquement</li>
                  <li><strong>Standard</strong> : Accès aux cours et aux enregistrements</li>
                  <li><strong>Premium</strong> : Accès complet (cours, enregistrements, sessions live)</li>
                </ul>
                Voulez-vous continuer ?
              </p>
            )}
          </Modal.Body>
          <Modal.Footer>
            {initSuccess ? (
              <Button variant="primary" onClick={() => {
                setShowInitModal(false);
                setInitSuccess(false);
              }}>
                Fermer
              </Button>
            ) : (
              <>
                <Button variant="secondary" onClick={() => setShowInitModal(false)} disabled={initializing}>
                  Annuler
                </Button>
                <Button variant="primary" onClick={initializeDefaultTypes} disabled={initializing}>
                  {initializing ? (
                    <>
                      <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" />
                      <span className="ms-2">Initialisation...</span>
                    </>
                  ) : (
                    "Initialiser"
                  )}
                </Button>
              </>
            )}
          </Modal.Footer>
        </Modal>
      </div>
    
  );
}

export default AfficherTypeAbonnement
