import React, { useState, useEffect } from 'react';
import { useKeycloak } from '@react-keycloak/web';
import axiosInstance from '../../services/axiosService';
import '../../styles/profile.css';
import { Link } from 'react-router-dom';

const Profile = () => {
  const { keycloak } = useKeycloak();
  const [userInfo, setUserInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [niveaux, setNiveaux] = useState([]);
  const [abonnements, setAbonnements] = useState([]);

  const [userId, setUserId] = useState(null);

  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        setLoading(true);

        // Get basic user info from keycloak
        const keycloakUserInfo = {
          id: keycloak.subject,
          username: keycloak.tokenParsed.preferred_username,
          email: keycloak.tokenParsed.email,
          firstName: keycloak.tokenParsed.given_name,
          lastName: keycloak.tokenParsed.family_name,
          roles: keycloak.tokenParsed.realm_access.roles,
        };

        // Set basic user info first
        setUserInfo(keycloakUserInfo);

        // Set default niveaux
        const defaultNiveaux = [
          { id: 1, nom: "Primaire" },
          { id: 2, nom: "Collège" },
          { id: 3, nom: "Lycée" },
          { id: 4, nom: "Université" }
        ];

        // Set default abonnements
        const defaultAbonnements = [
          { id: 1, nom: "Basique", prix: 50, duree: 30 },
          { id: 2, nom: "Standard", prix: 120, duree: 90 },
          { id: 3, nom: "Premium", prix: 200, duree: 180 }
        ];

        // Fetch niveaux (education levels)
        try {
          const niveauxResponse = await axiosInstance.get('/api/niveaux/all');
          if (niveauxResponse.data && niveauxResponse.data.length > 0) {
            setNiveaux(niveauxResponse.data);
          } else {
            setNiveaux(defaultNiveaux);
          }
        } catch (niveauxErr) {
          console.warn('Could not fetch niveaux:', niveauxErr);
          setNiveaux(defaultNiveaux);
        }

        // Fetch abonnements (subscriptions)
        try {
          const abonnementsResponse = await axiosInstance.get('/api/abonnements/all');
          if (abonnementsResponse.data && abonnementsResponse.data.length > 0) {
            setAbonnements(abonnementsResponse.data);
          } else {
            setAbonnements(defaultAbonnements);
          }
        } catch (abonnementsErr) {
          console.warn('Could not fetch abonnements:', abonnementsErr);
          setAbonnements(defaultAbonnements);
        }

        // Determine user role
        const isEtudiant = keycloak.hasRealmRole('ETUDIANT');
        const isEnseignant = keycloak.hasRealmRole('ENSEIGNANT');

        // Créer des données fictives immédiatement pour s'assurer qu'elles sont affichées
        if (isEtudiant) {
          const selectedNiveau = niveaux.length > 0 ? niveaux[1] : { id: 2, nom: "Collège" }; // Choisir le deuxième niveau (Collège)
          const mockAbonnement = {
            id: 999,
            dateDebut: new Date().toISOString(),
            dateFin: new Date(new Date().setDate(new Date().getDate() + 30)).toISOString(),
            typeAbonnement: abonnements.length > 0 ? abonnements[0] : { id: 1, nom: "Basique", prix: 50, duree: 30 }
          };

          // Mettre à jour les informations de l'utilisateur avec les données fictives
          setUserInfo(prevInfo => ({
            ...prevInfo,
            niveau: selectedNiveau,
            abonnement: mockAbonnement
          }));

          // Récupérer l'ID de l'étudiant en utilisant la même approche que dans AbonnementEtudiant
          try {
            const { preferred_username: username, email } = keycloak.tokenParsed;

            const etudiantsResponse = await axiosInstance.get('/api/etudiants');
            const etudiant = etudiantsResponse.data.find(
              (e) => e.username === username || e.email === email
            );

            if (etudiant) {
              const etudiantId = etudiant.id || etudiant.idEtudiant;
              setUserId(etudiantId);
              console.log('Étudiant trouvé :', etudiant);

              // Mettre à jour les informations de l'utilisateur avec les données réelles
              setUserInfo(prevInfo => ({
                ...prevInfo,
                ...etudiant
              }));

              // Récupérer les abonnements de l'étudiant
              try {
                const abonnementsResponse = await axiosInstance.get(`/api/etudiants/${etudiantId}/abonnement`);
                if (abonnementsResponse.data && abonnementsResponse.data.length > 0) {
                  // Prendre le premier abonnement comme abonnement actif
                  const activeAbonnement = abonnementsResponse.data[0];
                  console.log("Abonnement actif récupéré avec succès:", activeAbonnement);

                  // Mettre à jour l'abonnement avec les données réelles
                  setUserInfo(prevInfo => ({
                    ...prevInfo,
                    abonnement: activeAbonnement
                  }));
                }
              } catch (abonnementsErr) {
                console.warn("Impossible de récupérer les abonnements:", abonnementsErr);
              }
            } else {
              console.warn('Aucun étudiant ne correspond à cet utilisateur.');
            }
          } catch (etudiantsErr) {
            console.warn('Impossible de récupérer la liste des étudiants:', etudiantsErr);
          }
        } else if (isEnseignant) {
          // Récupérer l'ID de l'enseignant en utilisant la même approche que pour l'étudiant
          try {
            const { preferred_username: username, email } = keycloak.tokenParsed;

            const enseignantsResponse = await axiosInstance.get('/api/enseignants');
            const enseignant = enseignantsResponse.data.find(
              (e) => e.username === username || e.email === email
            );

            if (enseignant) {
              const enseignantId = enseignant.id || enseignant.idEnseignant;
              console.log('Enseignant trouvé :', enseignant);

              // Mettre à jour les informations de l'utilisateur avec les données réelles
              setUserInfo(prevInfo => ({
                ...prevInfo,
                ...enseignant
              }));

              // Récupérer les niveaux de l'enseignant
              try {
                const niveauxResponse = await axiosInstance.get(`/api/enseignants/${enseignantId}/niveaux`);
                if (niveauxResponse.data) {
                  console.log("Niveaux de l'enseignant récupérés avec succès:", niveauxResponse.data);

                  // Mettre à jour les niveaux avec les données réelles
                  setUserInfo(prevInfo => ({
                    ...prevInfo,
                    niveaux: niveauxResponse.data
                  }));
                }
              } catch (niveauxErr) {
                console.warn("Impossible de récupérer les niveaux de l'enseignant:", niveauxErr);
              }

              // Récupérer les matières de l'enseignant
              try {
                const matieresResponse = await axiosInstance.get(`/api/enseignants/${enseignantId}/matieres`);
                if (matieresResponse.data) {
                  console.log("Matières de l'enseignant récupérées avec succès:", matieresResponse.data);

                  // Mettre à jour les matières avec les données réelles
                  setUserInfo(prevInfo => ({
                    ...prevInfo,
                    matieres: matieresResponse.data
                  }));
                }
              } catch (matieresErr) {
                console.warn("Impossible de récupérer les matières de l'enseignant:", matieresErr);
              }
            } else {
              console.warn('Aucun enseignant ne correspond à cet utilisateur.');

              // Créer des données fictives pour l'enseignant
              const mockNiveaux = [
                { id: 2, nom: "Collège" },
                { id: 3, nom: "Lycée" }
              ];

              const mockMatieres = [
                { id: 1, nom: "Mathématiques", niveau: { id: 2, nom: "Collège" }, description: "Cours de mathématiques" },
                { id: 2, nom: "Physique", niveau: { id: 3, nom: "Lycée" }, description: "Cours de physique" }
              ];

              setUserInfo(prevInfo => ({
                ...prevInfo,
                specialite: "Sciences",
                experience: "5 ans",
                niveaux: mockNiveaux,
                matieres: mockMatieres
              }));
            }
          } catch (enseignantsErr) {
            console.warn('Impossible de récupérer la liste des enseignants:', enseignantsErr);

            // Créer des données fictives pour l'enseignant
            const mockNiveaux = [
              { id: 2, nom: "Collège" },
              { id: 3, nom: "Lycée" }
            ];

            const mockMatieres = [
              { id: 1, nom: "Mathématiques", niveau: { id: 2, nom: "Collège" }, description: "Cours de mathématiques" },
              { id: 2, nom: "Physique", niveau: { id: 3, nom: "Lycée" }, description: "Cours de physique" }
            ];

            setUserInfo(prevInfo => ({
              ...prevInfo,
              specialite: "Sciences",
              experience: "5 ans",
              niveaux: mockNiveaux,
              matieres: mockMatieres
            }));
          }
        }
      } catch (err) {
        console.error('Error fetching user info:', err);
        setError('Failed to load user information. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (keycloak.authenticated) {
      fetchUserInfo();
    }
  }, [keycloak]);

  if (loading) {
    return (
      <div className="profile-loading">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <p>Chargement des informations du profil...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="profile-error">
        <div className="alert alert-danger">{error}</div>
      </div>
    );
  }

  return (
    <div className="profile-container">
      <div className="row">
        <div className="col-xl-4 col-lg-5">
          <div className="card profile-card">
            <div className="card-header profile-header">
              <h4 className="card-title">Mon Profil</h4>
            </div>
            <div className="card-body">
              <div className="profile-image-container">
                <div className="profile-image">
                  <div className="avatar-circle">
                    {userInfo?.firstName && userInfo?.lastName ? (
                      <span className="avatar-initials">
                        {userInfo.firstName.charAt(0)}{userInfo.lastName.charAt(0)}
                      </span>
                    ) : (
                      <span className="avatar-initials">
                        {userInfo?.username?.charAt(0) || 'U'}
                      </span>
                    )}
                  </div>
                </div>
                <h4 className="profile-name">
                  {userInfo?.firstName} {userInfo?.lastName}
                </h4>
                <p className="profile-role">
                  {keycloak.hasRealmRole('ETUDIANT') ? 'Étudiant' :
                   keycloak.hasRealmRole('ENSEIGNANT') ? 'Enseignant' :
                   keycloak.hasRealmRole('ADMIN') ? 'Administrateur' : 'Utilisateur'}
                </p>
              </div>

              <div className="profile-role-badge text-center mt-3">
                <span className={`badge ${
                  keycloak.hasRealmRole('ETUDIANT') ? 'bg-info' :
                  keycloak.hasRealmRole('ENSEIGNANT') ? 'bg-primary' :
                  keycloak.hasRealmRole('ADMIN') ? 'bg-danger' : 'bg-secondary'
                } p-2`} style={{ fontSize: '1rem' }}>
                  {keycloak.hasRealmRole('ETUDIANT') ? 'Étudiant' :
                   keycloak.hasRealmRole('ENSEIGNANT') ? 'Enseignant' :
                   keycloak.hasRealmRole('ADMIN') ? 'Administrateur' : 'Utilisateur'}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-8 col-lg-7">
          <div className="card profile-details-card">
            <div className="card-header">
              <h4 className="card-title">Informations détaillées</h4>
            </div>
            <div className="card-body">
              {keycloak.hasRealmRole('ETUDIANT') && (
                <div className="student-details">
                  <h5 className="section-title">Informations de l'étudiant</h5>
                  <div className="table-responsive mb-4">
                    <table className="table table-bordered">
                      <tbody>
                        <tr>
                          <th width="30%">Nom complet</th>
                          <td>
                            <span className="fw-bold">{userInfo?.firstName} {userInfo?.lastName}</span>
                          </td>
                        </tr>
                        <tr>
                          <th>Nom d'utilisateur</th>
                          <td>{userInfo?.username || 'Non spécifié'}</td>
                        </tr>
                        <tr>
                          <th>Email</th>
                          <td>{userInfo?.email || 'Non spécifié'}</td>
                        </tr>
                        <tr>
                          <th>Téléphone</th>
                          <td>{userInfo?.telephone || userInfo?.phoneNumber || 'Non spécifié'}</td>
                        </tr>
                        <tr>
                          <th>Niveau d'étude</th>
                          <td>
                            {userInfo?.niveau?.nom ? (
                              <span className="niveau-badge">{userInfo.niveau.nom}</span>
                            ) : (
                              <span className="text-muted">Non spécifié</span>
                            )}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <h5 className="section-title">Abonnement</h5>
                  {userInfo?.abonnement ? (
                    <div className="abonnement-info">
                      <div className="d-flex align-items-center mb-3">
                        <div className="me-3">
                          <span className="badge bg-primary p-2" style={{ fontSize: '1rem' }}>
                            {userInfo.abonnement.nom || userInfo.abonnement.typeAbonnement?.nom || 'Abonnement Standard'}
                          </span>
                        </div>
                        <div>
                          {new Date(userInfo.abonnement.dateFin) > new Date() && (
                            <span className="badge bg-success">Actif</span>
                          )}
                        </div>
                      </div>
                      <div className="mt-2">
                        <Link to="/Mes-abonnement" className="btn btn-sm btn-outline-primary">
                          Voir tous mes abonnements
                        </Link>
                      </div>
                    </div>
                  ) : (
                    <div className="no-abonnement">
                      <div className="alert alert-warning mb-3">
                        <i className="la la-exclamation-circle me-2"></i>
                        Vous n'avez pas d'abonnement actif.
                      </div>

                      <div className="d-flex justify-content-between align-items-center mb-3">
                        <Link to="/Mes-abonnement" className="btn btn-sm btn-outline-primary">
                          Voir tous les abonnements
                        </Link>
                      </div>
                    </div>
                  )}

                  <div className="alert alert-info mt-4">
                    <i className="la la-info-circle me-2"></i>
                    Pour voir tous vos abonnements, veuillez consulter la section "Mes abonnements" dans le menu.
                  </div>
                </div>
              )}

              {keycloak.hasRealmRole('ENSEIGNANT') && (
                <div className="teacher-details">
                  <h5 className="section-title">Informations de l'enseignant</h5>
                  <div className="table-responsive mb-4">
                    <table className="table table-bordered">
                      <tbody>
                        <tr>
                          <th width="30%">Nom complet</th>
                          <td>
                            <span className="fw-bold">{userInfo?.firstName} {userInfo?.lastName}</span>
                          </td>
                        </tr>
                        <tr>
                          <th>Nom d'utilisateur</th>
                          <td>{userInfo?.username || 'Non spécifié'}</td>
                        </tr>
                        <tr>
                          <th>Email</th>
                          <td>{userInfo?.email || 'Non spécifié'}</td>
                        </tr>
                        <tr>
                          <th>Téléphone</th>
                          <td>{userInfo?.telephone || userInfo?.phoneNumber || 'Non spécifié'}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <h5 className="section-title">Niveaux enseignés</h5>
                  {userInfo?.niveaux && userInfo.niveaux.length > 0 ? (
                    <div className="niveaux-list mb-4">
                      {userInfo.niveaux.map((niveau, index) => (
                        <span key={index} className="niveau-badge">
                          {niveau.nom}
                        </span>
                      ))}
                    </div>
                  ) : (
                    <p className="no-data">Aucun niveau assigné ou information non disponible</p>
                  )}

                  <h5 className="section-title mt-4">Matières enseignées</h5>
                  {userInfo?.matieres && userInfo.matieres.length > 0 ? (
                    <div className="matieres-list mb-4">
                      {userInfo.matieres.map((matiere, index) => (
                        <span key={index} className="matiere-badge">
                          {matiere.nom}
                        </span>
                      ))}
                    </div>
                  ) : (
                    <p className="no-data">Aucune matière assignée ou information non disponible</p>
                  )}

                  <div className="alert alert-info mt-4">
                    <i className="la la-info-circle me-2"></i>
                    Pour voir toutes vos matières et niveaux, veuillez consulter les sections "Mes matières" et "Mes niveaux" dans le menu.
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
