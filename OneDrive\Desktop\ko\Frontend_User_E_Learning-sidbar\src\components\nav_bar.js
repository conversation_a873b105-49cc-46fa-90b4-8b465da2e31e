import React from "react";

const NavBar = () => {
  return (
    <div className="nav-header">
      <a href="#" className="brand-logo">
        <svg
          className="logo-abbr"
          xmlns="http://www.w3.org/2000/svg"
          xmlnsXlink="http://www.w3.org/1999/xlink"
          width="84"
          height="67"
          viewBox="0 0 84 67"
          fill="none"
        >
          <mask
            id="mask0_135_5"
            style={{ maskType: "alpha" }}
            maskUnits="userSpaceOnUse"
            x="4"
            y="0"
            width="76"
            height="66"
          >
            <rect x="4" width="76" height="66" fill="url(#pattern0)" />
          </mask>
          <g mask="url(#mask0_135_5)">
            <rect x="-3" y="-1" width="90" height="68" fill="none" />
          </g>
          <defs>
            <pattern
              id="pattern0"
              patternContentUnits="objectBoundingBox"
              width="1"
              height="1"
            >
              <use
                xlinkHref="#image0_135_5"
                transform="matrix(0.0125 0 0 0.0143939 0 -0.00378788)"
              />
            </pattern>
            <image
              id="image0_135_5"
              width="80"
              height="70"
              xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABGCAMAAABsQOMZAAAAllBMVEUAAAD/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxb/jxZ2RtyAAAAAMXRSTlMA61rOUwajmEgztPfHuqiLdj0gGhXw38SukX5pVi0lC4VhQjgD2b+fck4pD/nm0uIIbztwLQAAAiBJREFUWMPt19uSojAUheEoKigKqIACKqh4PnSv93+5YUprU0wwEMjFdFX/936SWIYd9tuPba5bG4XcTL8DGKwVcZvTHa+WewXcUAfyrF1b7oR/Wu7acEuUNJk25KYjfGhhNOB2xJWSK0nuwnEc2ZXgjCNqpK3qPt0YNTtsa3ArDRL5UQW31SDZ4Zx+5qInGuQ7canW3x7QsI7j8lzko0Ve6BY554GWeXZOxuEXFOQF89dhHHagKNO+MraC0hw2cw7quEeyZlndpxrOj/rsXW+sAuwV/sJHVSB1aUFOdGR1BcegVFqPrQnkXiPSjS9/d4xAjrQkF2u8ftUc5MlBfW6UcRzIN7RqcjvGxCB1FT8lDTqVYD/OBy4xZw3ZuzQWgZo1o6HL/sydiLsFB1e05C8guNJT2mYpp69pDk08IBWBWvEDrs2dlXd9Q1+YmAA6/SqwMKzekgJpBjPibGRVgvym3xwiTXtOO6wD9UBqOSXy/Hi92YhbDwAJkB9Wv88PM3S5sVYWpL9WVkzcfgDIg9TxTRK3BErALbKiKpCOJ2o6AUrB4SBrXwUS+V6LQRwHUmIwJ7fZNhHXBqQWxLUFqf8E9CDftwgMTWlPT5koN/Gl1hvQYabiJuCfYwX3PGrRk7nYBlWbeZK95M5DX7B19pXJl0bjcu7pxKxhxqjlVZlvE9wLnLVnbZs7dIvxkitTURotkPU835iyjEl2yv72U/sDJjPTtw93ZiUAAAAASUVORK5CYII="
            />
          </defs>
        </svg>
        <svg
          className="brand-title"
          xmlns="http://www.w3.org/2000/svg"
          width="122"
          height="19"
          viewBox="0 0 122 19"
          fill="none"
        >
          <path
            d="M5 4H8L9.5 7H10.5L12 4H15V9H13V6H9V9H7V6H5V4ZM17 4H19V9H17V4ZM21 4H25L27 7H25.5L24.5 5H22.5V9H21V4ZM29 4H31L33 7H34L36 4H39V9H37V6H33V9H31V6H29V4Z"
            fill="black"
          />
        </svg>
      </a>

      <div className="nav-control">
        <div className="hamburger">
          <span className="line"></span>
          <span className="line"></span>
          <span className="line"></span>
        </div>
      </div>
    </div>
  );
};

export default NavBar;
