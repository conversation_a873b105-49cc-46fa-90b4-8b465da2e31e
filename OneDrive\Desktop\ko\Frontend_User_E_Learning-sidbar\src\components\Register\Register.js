import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import myImage from "../../images/pattern/output-onlinepngtools.png";
import "./Register.css";

const Register = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // State pour gérer les données du formulaire
  const [formData, setFormData] = useState({
    nom: "",
    prenom: "",
    telephone: "",
    email: "",
    motDePasse: "",
    niveauEtudiant: "",
    dateNaissance: "",
  });

  // State pour afficher/masquer le mot de passe
  const [showPassword, setShowPassword] = useState(false);

  // Gérer les changements dans les champs du formulaire
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  // Gérer la soumission du formulaire
  const handleFormSubmit = (e) => {
    e.preventDefault();
    console.log("Formulaire soumis :", formData);

    // Réinitialisation du formulaire (optionnel)
    setFormData({
      nom: "",
      prenom: "",
      telephone: "",
      email: "",
      motDePasse: "",
      niveauEtudiant: "",
      dateNaissance: "",
    });
  };

  // Basculer l'affichage du mot de passe
  const togglePasswordVisibility = () => {
    setShowPassword((prevState) => !prevState);
  };

  return (
    <div className="fix-wrapper">
      <div className="container">
        <div className="row justify-content-center">
          <div className="col-lg-5 col-md-6">
            <div className="card mb-0 h-auto">
              <div className="card-body">
                {/* Logo et titre */}
                <div className="text-center mb-2">
                  <a href="/">
                    <svg width="250" height="56" viewBox="0 0 250 56">
                      <g transform="translate(20, 10)">
                        <text
                          x="70"
                          y="35"
                          fontSize="32"
                          fontFamily="Arial, sans-serif"
                          fontWeight="bold"
                        >
                          DEUTZA
                        </text>
                      </g>
                      <image href={myImage} x="0" y="-7" width="80" height="70" />
                    </svg>
                  </a>
                </div>

                <h4 className="text-center mb-4">Créer un compte</h4>

                {/* Formulaire d'inscription */}
                <form onSubmit={handleFormSubmit}>
                  {/* Nom */}
                  <div className="form-group">
                    <label className="form-label" htmlFor="nom">
                      Nom
                    </label>
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Entrez votre nom"
                      id="nom"
                      name="nom"
                      value={formData.nom}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  {/* Prénom */}
                  <div className="form-group">
                    <label className="form-label" htmlFor="prenom">
                      Prénom
                    </label>
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Entrez votre prénom"
                      id="prenom"
                      name="prenom"
                      value={formData.prenom}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  {/* Numéro de téléphone */}
                  <div className="form-group">
                    <label className="form-label" htmlFor="telephone">
                      Numéro de téléphone
                    </label>
                    <input
                      type="tel"
                      className="form-control"
                      placeholder="Entrez votre numéro de téléphone"
                      id="telephone"
                      name="telephone"
                      value={formData.telephone}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  {/* Email */}
                  <div className="form-group">
                    <label className="form-label" htmlFor="email">
                      Email
                    </label>
                    <input
                      type="email"
                      className="form-control"
                      placeholder="Entrez votre email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  {/* Mot de passe */}
                  <div className="mb-4 position-relative">
                    <label className="form-label" htmlFor="motDePasse">
                      Mot de passe
                    </label>
                    <input
                      type={showPassword ? "text" : "password"}
                      className="form-control"
                      placeholder="Entrez votre mot de passe"
                      id="motDePasse"
                      name="motDePasse"
                      value={formData.motDePasse}
                      onChange={handleInputChange}
                      required
                    />
                    <span
                      className="show-pass eye"
                      onClick={togglePasswordVisibility}
                      style={{ cursor: "pointer" }}
                    >
                      {showPassword ? (
                        <i className="fa fa-eye"></i>
                      ) : (
                        <i className="fa fa-eye-slash"></i>
                      )}
                    </span>
                  </div>

                  {/* Niveau d'étudiant */}
                  <div className="form-group">
                    <label className="form-label" htmlFor="niveauEtudiant">
                      Niveau d'étudiant
                    </label>
                    <select
                      className="form-control"
                      id="niveauEtudiant"
                      name="niveauEtudiant"
                      value={formData.niveauEtudiant}
                      onChange={handleInputChange}
                      required
                    >
                      <option value="">Sélectionnez votre niveau</option>
                      <option value="Licence 1">Licence 1</option>
                      <option value="Licence 2">Licence 2</option>
                      <option value="Licence 3">Licence 3</option>
                      <option value="Master 1">Master 1</option>
                      <option value="Master 2">Master 2</option>
                    </select>
                  </div>

                  {/* Date de naissance */}
                  <div className="form-group">
                    <label className="form-label" htmlFor="dateNaissance">
                      Date de naissance
                    </label>
                    <input
                      type="date"
                      className="form-control"
                      id="dateNaissance"
                      name="dateNaissance"
                      value={formData.dateNaissance}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  {/* Bouton de soumission */}
                  <div className="text-center mt-4">
                    <button type="submit" className="btn btn-primary btn-block">
                      S'inscrire
                    </button>
                  </div>
                </form>

                {/* Lien vers la connexion */}
                <div className="new-account mt-3">
                  <p>
                    Vous avez déjà un compte ?{" "}
                    <a className="text-primary" href="/login">
                      Connectez-vous
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;
