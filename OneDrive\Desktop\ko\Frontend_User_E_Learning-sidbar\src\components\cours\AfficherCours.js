import React from 'react';
import { useNavigate } from 'react-router-dom';

const AfficherCours = () => {
  const navigate = useNavigate();

  return (
      <div className="container-fluid">
        <div className="row page-titles mx-0">
          <div className="col-sm-6 p-md-0">
            <h4 style={{ color: "#37A7DF" }}>Liste des Cours</h4>
          </div>
          <div className="col-sm-6 p-md-0 justify-content-sm-end mt-2 mt-sm-0 d-flex">
            <button
              style={{
                backgroundColor: "var(--primary-blue)",
                borderColor: "var(--primary-blue)",
                color: "white",
                borderRadius: "6px",
                padding: "8px 15px",
                fontWeight: "500",
                transition: "all 0.3s ease",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
              }}
              className="btn"
              onClick={() => navigate("/AjouterCours")}
            >
              <i className="la la-plus-circle me-2"></i> Ajouter un Cours
            </button>
          </div>
        </div>
        <div className="row">
          {/* Course content will be implemented here */}
          <div className="col-12">
            <p>Implémentation à venir...</p>
          </div>
        </div>
      </div>
  );
};

export default AfficherCours;