import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useKeycloak } from '@react-keycloak/web';
import axiosInstance from '../../services/axiosService';

const QuestionDetail = () => {
  const { id } = useParams();
  const { keycloak } = useKeycloak();
  const navigate = useNavigate();
  const [question, setQuestion] = useState(null);
  const [reponses, setReponses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [reponseContenu, setReponseContenu] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [userId, setUserId] = useState(null);
  const [isAuthor, setIsAuthor] = useState(false);

  useEffect(() => {
    const fetchUserInfo = async () => {
      console.log("État d'authentification Keycloak:", keycloak.authenticated);
      console.log("Token Keycloak:", keycloak.token);

      if (keycloak.authenticated) {
        try {
          // Récupérer les informations de l'utilisateur depuis Keycloak
          const userInfo = await keycloak.loadUserInfo();
          console.log("Informations utilisateur Keycloak:", userInfo);

          // Utiliser le nom d'utilisateur Keycloak (preferred_username) comme identifiant
          setUserId(userInfo.preferred_username);
          console.log("Nom d'utilisateur défini:", userInfo.preferred_username);
          return userInfo.preferred_username;
        } catch (err) {
          console.error('Erreur lors de la récupération des informations utilisateur:', err);
          setError('Erreur lors de la récupération des informations utilisateur.');
          return null;
        }
      } else {
        console.error('Utilisateur non authentifié');
        return null;
      }
    };

    const fetchQuestionAndReponses = async (userId) => {
      setLoading(true);
      try {
        // Récupérer les détails de la question
        const questionResponse = await axiosInstance.get(`/api/forum/questions/${id}`);
        setQuestion(questionResponse.data);

        // Vérifier si l'utilisateur est l'auteur de la question
        if (userId && questionResponse.data.auteur.id === userId) {
          setIsAuthor(true);
        }

        // Récupérer les réponses à la question
        const reponsesResponse = await axiosInstance.get(`/api/forum/reponses/question/${id}`);
        setReponses(reponsesResponse.data);

        setError(null);
      } catch (err) {
        console.error('Erreur lors du chargement des données:', err);
        setError('Erreur lors du chargement des données. Veuillez réessayer plus tard.');
      } finally {
        setLoading(false);
      }
    };

    const init = async () => {
      const userId = await fetchUserInfo();
      await fetchQuestionAndReponses(userId);
    };

    init();
  }, [id, keycloak]);

  const handleReponseSubmit = async (e) => {
    e.preventDefault();

    if (!reponseContenu.trim()) {
      return;
    }

    if (!userId) {
      setError('Utilisateur non identifié. Veuillez vous reconnecter.');
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      const reponseData = {
        contenu: reponseContenu
      };

      const params = new URLSearchParams();
      params.append('questionId', id);
      params.append('userId', userId);

      const response = await axiosInstance.post(`/api/forum/reponses?${params.toString()}`, reponseData);

      // Ajouter la nouvelle réponse à la liste
      setReponses([...reponses, response.data]);

      // Réinitialiser le formulaire
      setReponseContenu('');
    } catch (err) {
      console.error('Erreur lors de l\'ajout de la réponse:', err);
      setError('Erreur lors de l\'ajout de la réponse. Veuillez réessayer plus tard.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleMarquerResolu = async (reponseId) => {
    try {
      const response = await axiosInstance.put(`/api/forum/questions/${id}/resolu/${reponseId}`);
      setQuestion(response.data);

      // Mettre à jour les réponses pour marquer celle qui est acceptée
      const updatedReponses = reponses.map(reponse => {
        if (reponse.id === reponseId) {
          return { ...reponse, acceptee: true };
        } else {
          return { ...reponse, acceptee: false };
        }
      });
      setReponses(updatedReponses);
    } catch (err) {
      console.error('Erreur lors du marquage de la question comme résolue:', err);
      setError('Erreur lors du marquage de la question comme résolue. Veuillez réessayer plus tard.');
    }
  };

  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = Math.abs(now - date);
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays < 1) {
        return "aujourd'hui";
      } else if (diffDays === 1) {
        return "hier";
      } else if (diffDays < 7) {
        return `il y a ${diffDays} jours`;
      } else {
        return date.toLocaleDateString('fr-FR');
      }
    } catch (error) {
      return 'Date inconnue';
    }
  };

  const formatDateFull = (dateString) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return 'Date inconnue';
    }
  };

  if (loading) {
    return (
      <div className="container-fluid">
        <div className="text-center my-5">
          <div className="spinner-border text-primary" role="status">
            <span className="sr-only">Chargement...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container-fluid">
        <div className="alert alert-danger my-5" role="alert">
          {error}
        </div>
      </div>
    );
  }

  if (!question) {
    return (
      <div className="container-fluid">
        <div className="alert alert-warning my-5" role="alert">
          Question non trouvée.
        </div>
      </div>
    );
  }

  // Ajouter un style pour s'assurer que le contenu est visible sans défilement excessif
  const containerStyle = {
    maxHeight: '100vh',
    overflowY: 'auto',
    padding: '20px'
  };

  return (
    <div className="container-fluid" style={containerStyle}>
      <div className="row page-titles mx-0">
        <div className="col-sm-6 p-md-0">
          <div className="welcome-text">
            <h4 style={{ color: "#37A7DF" }}>Question</h4>
            <p className="mb-0">Détails de la question et réponses</p>
          </div>
        </div>
        <div className="col-sm-6 p-md-0 justify-content-sm-end mt-2 mt-sm-0 d-flex">
          <ol className="breadcrumb">
            <li className="breadcrumb-item"><Link to="/dashboard">Accueil</Link></li>
            <li className="breadcrumb-item"><Link to="/forum">Forum</Link></li>
            <li className="breadcrumb-item active"><Link to={`/forum/question/${id}`}>Question</Link></li>
          </ol>
        </div>
      </div>

      <div className="row">
        <div className="col-lg-12">
          {/* Question - Version compacte */}
          <div className="card">
            <div className="card-header d-flex justify-content-between align-items-center">
              <div>
                <h4 className="card-title">{question.titre}</h4>
                <div>
                  <span className="badge badge-primary mr-2" style={{ backgroundColor: "#37A7DF" }}>
                    {question.matiere ? question.matiere.nomMatiere : 'Général'}
                  </span>
                  {question.resolu && (
                    <span className="badge badge-success">Résolu</span>
                  )}
                </div>
              </div>
              <div className="text-right">
                <small className="text-muted d-block">Posée par {question.auteur.firstName} {question.auteur.lastName} {formatDate(question.dateCreation)}</small>
                <small className="text-muted d-block">Vues: {question.vues}</small>
              </div>
            </div>
            <div className="card-body">
              <div className="question-content" style={{ whiteSpace: 'pre-line', maxHeight: '150px', overflowY: 'auto' }}>
                {question.contenu}
              </div>
              <div className="mt-2">
                <small className="text-muted">Publiée le {formatDateFull(question.dateCreation)}</small>
              </div>
            </div>
          </div>

          {/* Formulaire pour ajouter une réponse - Déplacé en haut pour être visible sans défilement */}
          <div className="card mb-4">
            <div className="card-header">
              <h4 className="card-title">Votre réponse</h4>
              {/* Afficher l'état d'authentification pour le débogage */}
              <small className="text-muted">
                État d'authentification: {keycloak.authenticated ? 'Authentifié' : 'Non authentifié'} |
                Utilisateur: {userId || 'Non défini'}
              </small>
            </div>
            <div className="card-body">
              {!keycloak.authenticated ? (
                <div className="alert alert-warning">
                  Vous devez être connecté pour répondre à cette question.
                  <button
                    className="btn btn-primary ml-3"
                    onClick={() => keycloak.login()}
                    style={{ backgroundColor: "#37A7DF", borderColor: "#37A7DF" }}
                  >
                    Se connecter
                  </button>
                </div>
              ) : (
                <form onSubmit={handleReponseSubmit}>
                  <div className="form-group">
                    <textarea
                      className="form-control"
                      rows="4"
                      placeholder="Écrivez votre réponse ici..."
                      value={reponseContenu}
                      onChange={(e) => setReponseContenu(e.target.value)}
                      required
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={submitting || !reponseContenu.trim()}
                      style={{ backgroundColor: "#37A7DF", borderColor: "#37A7DF" }}
                    >
                      {submitting ? (
                        <>
                          <span className="spinner-border spinner-border-sm mr-2" role="status" aria-hidden="true"></span>
                          Envoi en cours...
                        </>
                      ) : (
                        'Publier la réponse'
                      )}
                    </button>
                  </div>
                </form>
              )}
            </div>
          </div>

          {/* Réponses */}
          <div className="card">
            <div className="card-header">
              <h4 className="card-title">{reponses.length} Réponse{reponses.length !== 1 ? 's' : ''}</h4>
            </div>
            <div className="card-body">
              {reponses.length === 0 ? (
                <div className="text-center my-4">
                  <p>Aucune réponse pour le moment. Soyez le premier à répondre !</p>
                </div>
              ) : (
                reponses.map((reponse) => (
                  <div key={reponse.id} className={`card mb-3 ${reponse.acceptee ? 'border-success' : 'border-0'}`}>
                    <div className={`card-body ${reponse.acceptee ? 'bg-light' : ''}`}>
                      <div className="row">
                        <div className="col-md-2 col-lg-1 text-center">
                          <div className="avatar avatar-xl mb-3">
                            <div className="avatar-content" style={{ backgroundColor: reponse.acceptee ? "#248E39" : "#B7B7B7", color: "white", width: "50px", height: "50px", borderRadius: "50%", display: "flex", alignItems: "center", justifyContent: "center", fontSize: "20px" }}>
                              {reponse.auteur.firstName.charAt(0)}{reponse.auteur.lastName.charAt(0)}
                            </div>
                          </div>
                          <p className="mb-0">{reponse.auteur.firstName} {reponse.auteur.lastName}</p>
                          <small className="text-muted">{reponse.auteur.roles && reponse.auteur.roles.includes('ENSEIGNANT') ? 'Enseignant' : 'Étudiant'}</small>
                        </div>
                        <div className="col-md-10 col-lg-11">
                          {reponse.acceptee && (
                            <div className="alert alert-success mb-3">
                              <i className="fa fa-check-circle mr-2"></i> Réponse acceptée
                            </div>
                          )}
                          <div className="reponse-content" style={{ whiteSpace: 'pre-line' }}>
                            {reponse.contenu}
                          </div>
                          <div className="d-flex justify-content-between align-items-center mt-3">
                            <small className="text-muted">Répondu {formatDate(reponse.dateCreation)}</small>
                            {isAuthor && !question.resolu && (
                              <button
                                className="btn btn-sm btn-success"
                                onClick={() => handleMarquerResolu(reponse.id)}
                              >
                                Marquer comme solution
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuestionDetail;
