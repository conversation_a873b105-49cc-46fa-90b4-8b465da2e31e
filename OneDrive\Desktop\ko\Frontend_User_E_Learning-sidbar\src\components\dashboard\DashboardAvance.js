/**
 * ========================================================================
 * DASHBOARD AVANCÉ AVEC PIECHART - EXEMPLE D'IMPLÉMENTATION
 * ========================================================================
 * 
 * Ce fichier montre un exemple d'implémentation avancée du PieChart
 * dans un dashboard avec plusieurs options de visualisation.
 * 
 * FONCTIONNALITÉS :
 * - Onglets pour basculer entre différentes vues
 * - Layout responsive optimisé
 * - Contrôles utilisateur pour personnaliser l'affichage
 * - Gestion d'état avancée
 * 
 * UTILISATION :
 * Remplacez le contenu de dashboard.js par ce code pour tester
 * les fonctionnalités avancées.
 * 
 * <AUTHOR> de développement ThinkTrend
 * @version 1.0
 * @since 2025-01-24
 * ========================================================================
 */

import React, { useEffect, useState } from "react";
import axiosInstance from "../../services/axiosService";
import "../../styles/theme.css";
import MatieresParAbonnementChart from "./MatieresParAbonnementChart";
import PieChart from "./Pie_chart";
import { Chart as ChartJS } from "chart.js";
import CustomChart from "./chart";

/**
 * Composant Dashboard Avancé avec PieChart
 */
const DashboardAvance = () => {
  // ====================================================================
  // ÉTATS DU COMPOSANT
  // ====================================================================
  
  // Données statistiques
  const [stats, setStats] = useState({
    totalCours: 0,
    totalAbonnements: 0,
    totalNiveaux: 0,
    totalMatiere: 0,
  });

  // Gestion des erreurs
  const [error, setError] = useState(null);
  
  // Onglet actif
  const [activeTab, setActiveTab] = useState('overview');
  
  // Type de graphique sélectionné
  const [chartType, setChartType] = useState('pie');
  
  // Mode d'affichage (compact ou étendu)
  const [viewMode, setViewMode] = useState('compact');

  // ====================================================================
  // RÉCUPÉRATION DES DONNÉES
  // ====================================================================
  
  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await axiosInstance.get("/api/statistics");
        setStats(response.data);
        console.log("📊 Statistiques chargées:", response.data);
      } catch (err) {
        console.error("❌ Erreur chargement statistiques:", err);
        setError("Erreur lors du chargement des statistiques.");
      }
    };
    fetchStats();
  }, []);

  // ====================================================================
  // CONFIGURATION DES CARTES STATISTIQUES
  // ====================================================================
  
  const cards = [
    {
      label: "Cours",
      value: stats.totalCours,
      icon: "la-graduation-cap",
      color: "#37A7DF",
      description: "Cours disponibles"
    },
    {
      label: "Abonnements",
      value: stats.totalAbonnements,
      icon: "la-credit-card",
      color: "#F2BC00",
      description: "Types d'abonnements"
    },
    {
      label: "Niveaux",
      value: stats.totalNiveaux,
      icon: "la-layer-group",
      color: "#248E39",
      description: "Niveaux d'étude"
    },
    {
      label: "Matières",
      value: stats.totalMatiere,
      icon: "la-book-open",
      color: "#000080",
      description: "Matières enseignées"
    },
  ];

  // ====================================================================
  // FONCTIONS UTILITAIRES
  // ====================================================================
  
  /**
   * Fonction pour changer d'onglet
   */
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    console.log("🔄 Changement d'onglet:", tab);
  };

  /**
   * Fonction pour changer le type de graphique
   */
  const handleChartTypeChange = (type) => {
    setChartType(type);
    console.log("📊 Changement de graphique:", type);
  };

  // ====================================================================
  // RENDU DU COMPOSANT
  // ====================================================================
  
  return (
    <div style={{ minHeight: "100vh", padding: "2rem", backgroundColor: "#F6F4EE" }}>
      
      {/* Gestion des erreurs */}
      {error && (
        <div className="alert alert-danger mb-4" style={{ borderLeft: "4px solid #dc3545" }}>
          <i className="fa fa-exclamation-triangle me-2"></i>
          {error}
        </div>
      )}

      {/* En-tête du Dashboard */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 style={{ color: "#37A7DF", fontWeight: "700" }}>
                📊 Dashboard Avancé
              </h2>
              <p style={{ color: "#1D1D1B", margin: "0" }}>
                Tableau de bord interactif avec graphiques personnalisables
              </p>
            </div>
            
            {/* Contrôles d'affichage */}
            <div className="btn-group" role="group">
              <button
                type="button"
                className={`btn ${viewMode === 'compact' ? 'btn-primary' : 'btn-outline-primary'}`}
                onClick={() => setViewMode('compact')}
                style={{ 
                  backgroundColor: viewMode === 'compact' ? '#37A7DF' : 'transparent',
                  borderColor: '#37A7DF'
                }}
              >
                📱 Compact
              </button>
              <button
                type="button"
                className={`btn ${viewMode === 'extended' ? 'btn-primary' : 'btn-outline-primary'}`}
                onClick={() => setViewMode('extended')}
                style={{ 
                  backgroundColor: viewMode === 'extended' ? '#37A7DF' : 'transparent',
                  borderColor: '#37A7DF'
                }}
              >
                🖥️ Étendu
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Cartes statistiques (conditionnelles) */}
      {viewMode === 'extended' && (
        <div className="row g-4 mb-5">
          {cards.map((card, i) => (
            <div key={i} className="col-12 col-sm-6 col-lg-3">
              <div
                className="card h-100 text-white shadow-sm border-0"
                style={{
                  borderRadius: "16px",
                  backgroundColor: card.color,
                  transition: "all 0.3s ease",
                  cursor: "pointer"
                }}
                onMouseOver={(e) => e.currentTarget.style.transform = "translateY(-5px)"}
                onMouseOut={(e) => e.currentTarget.style.transform = "translateY(0)"}
              >
                <div className="card-body d-flex align-items-center">
                  <div
                    className="icon-wrapper me-3"
                    style={{
                      backgroundColor: "rgba(255, 255, 255, 0.2)",
                      width: 55,
                      height: 55,
                      borderRadius: "50%",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <i className={`la ${card.icon} fs-4 text-white`}></i>
                  </div>
                  <div>
                    <p className="mb-1" style={{ fontSize: "0.9rem", opacity: "0.85" }}>
                      {card.description}
                    </p>
                    <h4 className="mb-0" style={{ fontWeight: "700" }}>
                      {card.value}
                    </h4>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Navigation par onglets */}
      <div className="row mb-4">
        <div className="col-12">
          <ul className="nav nav-tabs" style={{ borderBottom: "2px solid #37A7DF" }}>
            <li className="nav-item">
              <button 
                className={`nav-link ${activeTab === 'overview' ? 'active' : ''}`}
                onClick={() => handleTabChange('overview')}
                style={{ 
                  color: activeTab === 'overview' ? '#37A7DF' : '#1D1D1B',
                  borderColor: activeTab === 'overview' ? '#37A7DF' : 'transparent',
                  fontWeight: "500"
                }}
              >
                🥧 Vue d'ensemble
              </button>
            </li>
            <li className="nav-item">
              <button 
                className={`nav-link ${activeTab === 'details' ? 'active' : ''}`}
                onClick={() => handleTabChange('details')}
                style={{ 
                  color: activeTab === 'details' ? '#37A7DF' : '#1D1D1B',
                  borderColor: activeTab === 'details' ? '#37A7DF' : 'transparent',
                  fontWeight: "500"
                }}
              >
                📈 Analyses détaillées
              </button>
            </li>
            <li className="nav-item">
              <button 
                className={`nav-link ${activeTab === 'custom' ? 'active' : ''}`}
                onClick={() => handleTabChange('custom')}
                style={{ 
                  color: activeTab === 'custom' ? '#37A7DF' : '#1D1D1B',
                  borderColor: activeTab === 'custom' ? '#37A7DF' : 'transparent',
                  fontWeight: "500"
                }}
              >
                ⚙️ Personnalisé
              </button>
            </li>
          </ul>
        </div>
      </div>

      {/* Contenu des onglets */}
      <div className="tab-content">
        
        {/* Onglet Vue d'ensemble */}
        {activeTab === 'overview' && (
          <div className="row">
            <div className="col-lg-6 col-12 mb-4">
              <div style={{ 
                backgroundColor: "white", 
                borderRadius: "15px", 
                padding: "10px",
                boxShadow: "0 4px 15px rgba(0,0,0,0.1)"
              }}>
                <PieChart/>
              </div>
            </div>
            <div className="col-lg-6 col-12 mb-4">
              <div style={{ 
                backgroundColor: "white", 
                borderRadius: "15px", 
                padding: "10px",
                boxShadow: "0 4px 15px rgba(0,0,0,0.1)"
              }}>
                <MatieresParAbonnementChart/>
              </div>
            </div>
          </div>
        )}
        
        {/* Onglet Analyses détaillées */}
        {activeTab === 'details' && (
          <div className="row">
            <div className="col-12 mb-4">
              <div style={{ 
                backgroundColor: "white", 
                borderRadius: "15px", 
                padding: "20px",
                boxShadow: "0 4px 15px rgba(0,0,0,0.1)"
              }}>
                <CustomChart/>
              </div>
            </div>
          </div>
        )}
        
        {/* Onglet Personnalisé */}
        {activeTab === 'custom' && (
          <div className="row">
            <div className="col-12 mb-3">
              {/* Contrôles de sélection */}
              <div className="d-flex justify-content-center mb-4">
                <div className="btn-group" role="group">
                  <button
                    type="button"
                    className={`btn ${chartType === 'pie' ? 'btn-primary' : 'btn-outline-primary'}`}
                    onClick={() => handleChartTypeChange('pie')}
                    style={{ 
                      backgroundColor: chartType === 'pie' ? '#37A7DF' : 'transparent',
                      borderColor: '#37A7DF'
                    }}
                  >
                    🥧 Graphique en Secteurs
                  </button>
                  <button
                    type="button"
                    className={`btn ${chartType === 'bar' ? 'btn-primary' : 'btn-outline-primary'}`}
                    onClick={() => handleChartTypeChange('bar')}
                    style={{ 
                      backgroundColor: chartType === 'bar' ? '#37A7DF' : 'transparent',
                      borderColor: '#37A7DF'
                    }}
                  >
                    📊 Graphique en Barres
                  </button>
                  <button
                    type="button"
                    className={`btn ${chartType === 'custom' ? 'btn-primary' : 'btn-outline-primary'}`}
                    onClick={() => handleChartTypeChange('custom')}
                    style={{ 
                      backgroundColor: chartType === 'custom' ? '#37A7DF' : 'transparent',
                      borderColor: '#37A7DF'
                    }}
                  >
                    📈 Graphique Personnalisé
                  </button>
                </div>
              </div>

              {/* Affichage conditionnel des graphiques */}
              <div style={{ 
                backgroundColor: "white", 
                borderRadius: "15px", 
                padding: "20px",
                boxShadow: "0 4px 15px rgba(0,0,0,0.1)"
              }}>
                {chartType === 'pie' && <PieChart/>}
                {chartType === 'bar' && <MatieresParAbonnementChart/>}
                {chartType === 'custom' && <CustomChart/>}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Section de bienvenue */}
      <div className="row mt-5">
        <div className="col-12">
          <div
            className="card shadow-sm border-0"
            style={{
              borderLeft: "6px solid #37A7DF",
              backgroundColor: "#EEF9F5",
              borderRadius: "16px",
            }}
          >
            <div className="card-body">
              <h5 className="mb-2" style={{ fontWeight: "600", color: "#1D1D1B" }}>
                🎉 Dashboard Avancé Configuré !
              </h5>
              <p style={{ color: "#248E39", fontSize: "0.95rem", margin: "0" }}>
                Votre nouveau PieChart est maintenant intégré avec succès. 
                Explorez les différents onglets et modes d'affichage pour découvrir toutes les fonctionnalités.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardAvance;
