/* ChatModel.css */

.chat-container::-webkit-scrollbar {
  width: 8px;
}

.chat-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.chat-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.chat-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.user-message {
  animation: fadeIn 0.3s ease-in-out;
}

.ai-message {
  animation: fadeIn 0.5s ease-in-out;
}

.error-message {
  animation: fadeIn 0.5s ease-in-out;
  border-left: 3px solid #dc3545 !important;
}

.fallback-message {
  animation: fadeIn 0.5s ease-in-out;
  border-left: 3px solid #ffc107 !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.suggestions-container {
  display: flex;
  flex-wrap: wrap;
}

.tips-list {
  padding-left: 20px;
}

.tips-list li {
  margin-bottom: 10px;
  color: #555;
}
