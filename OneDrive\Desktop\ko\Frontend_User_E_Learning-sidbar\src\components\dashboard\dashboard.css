:root {
  --blue: #37A7DF;
  --yellow: #F2BC00;
  --gray: #B7B7B7;
  --light-green: #F6F4EE;
  --green: #248E39;
  --dark: #1D1D1B;
  --navy: #000080;
  --beige: #F6F4EE;
}

body {
  background-color: var(--light-green);
  font-family: "Segoe UI", sans-serif;
  margin: 0;
}

.dashboard-container {
  padding: 2rem;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.5rem;
}

.dashboard-card {
  background-color: var(--beige);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.2s;
}

.dashboard-card:hover {
  transform: translateY(-5px);
}

.icon-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
}

.label {
  margin: 0;
  font-size: 0.9rem;
  color: var(--gray);
}

.value {
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--dark);
}

.welcome-box {
  margin-top: 3rem;
  background-color: var(--beige);
  padding: 1.5rem;
  border-left: 5px solid var(--blue);
  border-radius: 1rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

.welcome-box h4 {
  margin-bottom: 0.5rem;
  color: var(--dark);
}

.welcome-box p {
  margin: 0;
  color: var(--gray);
}

.error-box {
  background-color: #ffdddd;
  color: #a30000;
  padding: 1rem;
  border-radius: 8px;
  margin: 2rem;
}
.dashboard-container {
  padding: 20px;
}

.dashboard-title {
  margin-bottom: 30px;
  color: #333;
  font-weight: 600;
}

.stat-card {
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card .card-body {
  padding: 20px;
  text-align: center;
}

.stat-title {
  color: #6c757d;
  font-size: 16px;
  margin-bottom: 10px;
}

.stat-value {
  color: #343a40;
  font-size: 28px;
  font-weight: 700;
}

/* Couleurs spécifiques pour chaque carte */
.stat-card.matieres {
  background-color: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.stat-card.abonnements {
  background-color: #e8f5e9;
  border-left: 4px solid #4caf50;
}

.stat-card.niveaux {
  background-color: #fff3e0;
  border-left: 4px solid #ff9800;
}

.stat-card.cours {
  background-color: #f3e5f5;
  border-left: 4px solid #9c27b0;
}

.stat-card.chapitres {
  background-color: #e0f7fa;
  border-left: 4px solid #00bcd4;
}

.stat-card.etudiants {
  background-color: #fff8e1;
  border-left: 4px solid #ffc107;
}

.stat-card.enseignants {
  background-color: #f1f8e9;
  border-left: 4px solid #8bc34a;
}
