/**
 * Chat Model Component using OpenAI API
 *
 * This component provides an interactive chat interface that uses the OpenAI API
 * to generate responses to user messages. It includes:
 * - A chat interface with message history
 * - The ability to send messages to the AI model
 * - A history of past conversations
 *
 * API Key: ********************************************************************************************************************************************************************
 */

import React, { useState, useEffect, useRef } from 'react';
import { useKeycloak } from '@react-keycloak/web';
import axiosInstance from '../../services/axiosService';
import chatService from '../../services/chatService';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import './ChatModel.css';

const ChatModel = () => {
  const { keycloak } = useKeycloak();
  const [userId, setUserId] = useState(null);
  const [message, setMessage] = useState('');
  const [chatHistory, setChatHistory] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const chatContainerRef = useRef(null);

  // Get user ID on component mount
  useEffect(() => {
    if (keycloak.authenticated) {
      const fetchEtudiantId = async () => {
        try {
          const { preferred_username: username, email } = keycloak.tokenParsed;

          const response = await axiosInstance.get('/api/etudiants');
          const etudiant = response.data.find(
            (e) => e.username === username || e.email === email
          );

          if (etudiant) {
            setUserId(etudiant.id || etudiant.idEtudiant);
            // Fetch history after getting user ID
            fetchChatHistory(etudiant.id || etudiant.idEtudiant);
          } else {
            if (response.data.length > 0) {
              const firstEtudiant = response.data[0];
              setUserId(firstEtudiant.id || firstEtudiant.idEtudiant);
              fetchChatHistory(firstEtudiant.id || firstEtudiant.idEtudiant);
            } else {
              setError('Aucun étudiant trouvé dans le système.');
            }
          }
        } catch (error) {
          console.error('Erreur lors de la récupération des étudiants :', error);
          setError('Impossible de récupérer la liste des étudiants.');
        }
      };

      fetchEtudiantId();
    }
  }, [keycloak]);

  // Fetch user's chat history
  const fetchChatHistory = async (userId) => {
    try {
      const history = await chatService.getHistory(userId);
      setChatHistory(history);
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique du chat:', error);
      // Don't show error to user, just log it
    }
  };

  // Handle sending a message
  const handleSendMessage = async (e) => {
    e.preventDefault();

    if (!message.trim()) {
      setError('Veuillez entrer un message.');
      return;
    }

    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      // Add user message to chat history immediately for better UX
      const userMessage = {
        userMessage: message,
        aiResponse: null,
        createdAt: new Date().toISOString(),
        isLoading: true
      };

      setChatHistory(prevHistory => [userMessage, ...prevHistory]);

      // Send message to API
      const response = await chatService.sendMessage(message, userId);

      // Update chat history with AI response
      if (response.success) {
        // Remove the temporary message and add the complete conversation
        setChatHistory(prevHistory => {
          const updatedHistory = prevHistory.filter(msg => !msg.isLoading);
          return [{
            userMessage: message,
            aiResponse: response.response,
            createdAt: new Date().toISOString(),
            isFallback: response.isFallback || false
          }, ...updatedHistory];
        });

        setMessage(''); // Clear input field

        // Show different success message for fallback responses
        if (response.isFallback) {
          setSuccess('Message traité avec une réponse locale (mode hors ligne)');
        } else {
          setSuccess('Message envoyé avec succès!');
        }
      } else {
        // Check for specific error types
        if (response.errorType === 'quota_exceeded') {
          // For quota exceeded errors, still show the user message and the error as an AI response
          setChatHistory(prevHistory => {
            const updatedHistory = prevHistory.filter(msg => !msg.isLoading);
            return [{
              userMessage: message,
              aiResponse: response.friendlyMessage || "Le quota d'utilisation de l'API OpenAI a été dépassé. Veuillez contacter l'administrateur.",
              createdAt: new Date().toISOString(),
              isError: true
            }, ...updatedHistory];
          });

          setError(
            <div>
              <p><strong>Erreur de quota API :</strong> {response.friendlyMessage}</p>
              <p>Le quota d'utilisation de l'API OpenAI a été dépassé. Cela peut être dû à :</p>
              <ul>
                <li>Un dépassement du nombre de requêtes autorisées</li>
                <li>Un problème de facturation sur le compte OpenAI</li>
              </ul>
              <p>Veuillez contacter l'administrateur pour résoudre ce problème.</p>
            </div>
          );
        } else if (response.errorType === 'invalid_api_key') {
          // For invalid API key errors
          setChatHistory(prevHistory => prevHistory.filter(msg => !msg.isLoading));
          setError(
            <div>
              <p><strong>Erreur de clé API :</strong> {response.friendlyMessage}</p>
              <p>La clé API OpenAI est invalide ou a expiré. Veuillez contacter l'administrateur.</p>
            </div>
          );
        } else {
          // For other errors
          setChatHistory(prevHistory => prevHistory.filter(msg => !msg.isLoading));
          setError(response.friendlyMessage || response.message || 'Erreur lors de l\'envoi du message.');
        }
      }
    } catch (error) {
      // Remove the temporary message
      setChatHistory(prevHistory => prevHistory.filter(msg => !msg.isLoading));
      console.error('Erreur lors de l\'envoi du message:', error);
      setError('Erreur lors de l\'envoi du message. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  // Scroll to bottom of chat container when new messages arrive
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = 0;
    }
  }, [chatHistory]);

  return (
    <div className="position-relative">
      <div className="container-fluid px-4" style={{ backgroundColor: "#F6F4EE", minHeight: "100vh" }}>
        <div className="row page-titles mx-0 d-flex align-items-center justify-content-between flex-wrap">
          <div className="col-auto">
            <h4 className="fw-bold" style={{ color: "#000080" }}>
              Assistant IA (OpenAI)
            </h4>
          </div>
        </div>

        {error && (
          <Alert variant="danger" className="d-flex align-items-center">
            <div className="flex-shrink-0 me-3">
              <i className="la la-exclamation-circle" style={{ fontSize: '2rem' }}></i>
            </div>
            <div className="flex-grow-1">
              {error}
            </div>
            <div className="flex-shrink-0 ms-3">
              <Button
                variant="outline-danger"
                size="sm"
                onClick={() => setError('')}
              >
                Fermer
              </Button>
            </div>
          </Alert>
        )}
        {success && <Alert variant="success">{success}</Alert>}

        <div className="row">
          <div className="col-lg-8 col-md-12 mb-4">
            <Card className="shadow-sm h-100 border-0">
              <Card.Body>
                <Card.Title style={{ color: "#37A7DF" }}>Discutez avec l'assistant IA</Card.Title>

                {/* Chat Messages Display */}
                <div
                  ref={chatContainerRef}
                  className="chat-container mb-3"
                  style={{
                    height: "400px",
                    overflowY: "auto",
                    display: "flex",
                    flexDirection: "column-reverse",
                    backgroundColor: "#EEF9F5",
                    borderRadius: "8px",
                    padding: "15px"
                  }}
                >
                  {chatHistory.length > 0 ? (
                    <div>
                      {chatHistory.map((chat, index) => (
                        <div key={index} className="mb-3">
                          {/* User Message */}
                          <div className="d-flex justify-content-end mb-2">
                            <div
                              className="user-message p-3"
                              style={{
                                backgroundColor: "#37A7DF",
                                color: "white",
                                borderRadius: "15px 15px 0 15px",
                                maxWidth: "80%"
                              }}
                            >
                              {chat.userMessage}
                            </div>
                          </div>

                          {/* AI Response */}
                          {chat.isLoading ? (
                            <div className="d-flex justify-content-start mb-2">
                              <div
                                className="ai-message p-3"
                                style={{
                                  backgroundColor: "#f0f0f0",
                                  borderRadius: "15px 15px 15px 0",
                                  maxWidth: "80%"
                                }}
                              >
                                <Spinner animation="border" size="sm" /> Réflexion en cours...
                              </div>
                            </div>
                          ) : chat.aiResponse && (
                            <div className="d-flex justify-content-start mb-2">
                              <div
                                className={`ai-message p-3 ${chat.isError ? 'error-message' : ''} ${chat.isFallback ? 'fallback-message' : ''}`}
                                style={{
                                  backgroundColor: chat.isError ? "#fff8f8" : chat.isFallback ? "#fffbf0" : "#f0f0f0",
                                  borderRadius: "15px 15px 15px 0",
                                  maxWidth: "80%",
                                  borderLeft: chat.isError ? "3px solid #dc3545" : chat.isFallback ? "3px solid #ffc107" : "none"
                                }}
                              >
                                {chat.isError && (
                                  <div className="mb-2">
                                    <i className="la la-exclamation-circle text-danger me-2"></i>
                                    <span className="text-danger fw-bold">Erreur</span>
                                  </div>
                                )}
                                {chat.isFallback && (
                                  <div className="mb-2">
                                    <i className="la la-info-circle text-warning me-2"></i>
                                    <span className="text-warning fw-bold">Réponse locale</span>
                                  </div>
                                )}
                                {chat.aiResponse.split('\n').map((line, i) => (
                                  <React.Fragment key={i}>
                                    {line}
                                    {i < chat.aiResponse.split('\n').length - 1 && <br />}
                                  </React.Fragment>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center text-muted h-100 d-flex align-items-center justify-content-center">
                      <p>Posez une question pour commencer la conversation.</p>
                    </div>
                  )}
                </div>

                {/* Message Input Form */}
                <Form onSubmit={handleSendMessage}>
                  <Form.Group className="mb-3">
                    <Form.Control
                      as="textarea"
                      rows={3}
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      placeholder="Tapez votre message ici..."
                      style={{ backgroundColor: "#EEF9F5" }}
                    />
                  </Form.Group>

                  <Button
                    type="submit"
                    className="btn rounded-pill shadow-sm px-4"
                    style={{
                      backgroundColor: "#F2BC00",
                      color: "#1D1D1B",
                      border: "none",
                    }}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                        />
                        <span className="ms-2">Envoi en cours...</span>
                      </>
                    ) : (
                      "Envoyer"
                    )}
                  </Button>
                </Form>
              </Card.Body>
            </Card>
          </div>

          <div className="col-lg-4 col-md-12 mb-4">
            <Card className="shadow-sm h-100 border-0">
              <Card.Body>
                <Card.Title style={{ color: "#37A7DF" }}>Suggestions de questions</Card.Title>
                <div className="suggestions-container">
                  <Button
                    variant="outline-primary"
                    className="mb-2 me-2"
                    onClick={() => setMessage("Comment puis-je améliorer mes compétences en programmation?")}
                  >
                    Améliorer mes compétences en programmation
                  </Button>
                  <Button
                    variant="outline-primary"
                    className="mb-2 me-2"
                    onClick={() => setMessage("Explique-moi le concept de l'intelligence artificielle.")}
                  >
                    Concept de l'intelligence artificielle
                  </Button>
                  <Button
                    variant="outline-primary"
                    className="mb-2 me-2"
                    onClick={() => setMessage("Quelles sont les meilleures pratiques pour étudier efficacement?")}
                  >
                    Étudier efficacement
                  </Button>
                  <Button
                    variant="outline-primary"
                    className="mb-2 me-2"
                    onClick={() => setMessage("Comment fonctionne le machine learning?")}
                  >
                    Fonctionnement du machine learning
                  </Button>
                  <Button
                    variant="outline-primary"
                    className="mb-2 me-2"
                    onClick={() => setMessage("Quels sont les langages de programmation les plus demandés en 2025?")}
                  >
                    Langages de programmation populaires
                  </Button>
                </div>

                <div className="mt-4">
                  <Card.Title style={{ color: "#37A7DF" }}>Conseils d'utilisation</Card.Title>
                  <ul className="tips-list">
                    <li>Posez des questions claires et spécifiques pour obtenir les meilleures réponses.</li>
                    <li>L'assistant peut vous aider avec des questions académiques, des explications de concepts, et plus encore.</li>
                    <li>Pour des réponses plus précises, fournissez du contexte dans votre question.</li>
                    <li>L'assistant utilise l'API OpenAI pour générer des réponses basées sur l'IA.</li>
                  </ul>

                  <div className="mt-3 p-2" style={{ backgroundColor: "#f8f9fa", borderRadius: "8px", fontSize: "0.9rem" }}>
                    <p className="mb-1"><strong>Note sur les limitations :</strong></p>
                    <p className="mb-2">L'API OpenAI a des quotas d'utilisation. En cas de dépassement de quota, le système basculera automatiquement vers un mode de réponses locales (indiqué par <span className="text-warning">Réponse locale</span>).</p>
                    <p className="mb-0">Les réponses locales sont limitées à certains sujets comme la programmation, l'intelligence artificielle, les techniques d'étude, et le machine learning.</p>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatModel;
