import React from "react";

const Emploi = () => {
  return (
    <div className="content-body">
      {/* Début du conteneur principal */}
      <div className="container-fluid">
        {/* Section des titres de page */}
        <div className="row page-titles mx-0">
          <div className="col-sm-6 p-md-0">
            <div className="welcome-text">
              <h4>Events</h4> {/* Titre principal de la section */}
            </div>
          </div>
          <div className="col-sm-6 p-md-0 justify-content-sm-end mt-2 mt-sm-0 d-flex">
            <ol className="breadcrumb">
              <li className="breadcrumb-item">
                <a href="javascript:void(0)">Events</a> {/* Lien vers Events */}
              </li>
              <li className="breadcrumb-item active">
                <a href="javascript:void(0)">Events Management</a>{" "}
                {/* Lien vers la gestion des événements */}
              </li>
            </ol>
          </div>
        </div>

        {/* Section des événements */}
        <div className="row">
          <div className="col-xl-9 col-xxl-8">
            <div className="card">
              <div className="card-body">
                {/* Calendrier pour les événements */}
                <div id="calendar" className="app-fullcalendar"></div>
              </div>
            </div>
          </div>

          {/* Section pour ajouter un événement ou une catégorie */}
          <div className="col-xl-3 col-xxl-4">
            <div className="card">
              <div className="card-body">
                <h4 className="card-intro-title">Calendar</h4>

                {/* Div pour les événements externes */}
                <div>
                  <div id="external-events" className="my-3">
                    <p>Drag and drop your event or click in the calendar</p>{" "}
                    {/* Instruction pour l'utilisateur */}
                    {/* Liste d'événements pouvant être ajoutés */}
                    <div
                      className="external-event btn-primary light"
                      data-class="bg-primary"
                    >
                      <span>New Theme Release</span>
                    </div>
                    <div
                      className="external-event btn-warning light"
                      data-class="bg-warning"
                    >
                      My Event
                    </div>
                    <div
                      className="external-event btn-danger light"
                      data-class="bg-danger"
                    >
                      Meet manager
                    </div>
                    <div
                      className="external-event btn-info light"
                      data-class="bg-info"
                    >
                      Create New theme
                    </div>
                    <div
                      className="external-event btn-dark light"
                      data-class="bg-dark"
                    >
                      Project Launch
                    </div>
                    <div
                      className="external-event btn-secondary light"
                      data-class="bg-secondary"
                    >
                      Meeting
                    </div>
                  </div>

                  {/* Case à cocher pour supprimer après le déplacement */}
                  <div className="checkbox form-check checkbox-event custom-checkbox pt-3 pb-2">
                    <input
                      type="checkbox"
                      className="form-check-input"
                      id="drop-remove"
                    />
                    <label className="form-check-label" htmlFor="drop-remove">
                      Remove After Drop {/* Label pour la case à cocher */}
                    </label>
                  </div>

                  {/* Bouton pour créer un nouvel événement */}
                  <a
                    href="javascript:void(0)"
                    data-bs-toggle="modal"
                    data-bs-target="#add-category"
                    className="btn btn-primary btn-event w-100"
                  >
                    <span className="align-middle">
                      <i className="ti-plus me-1"></i>
                    </span>{" "}
                    Create New {/* Bouton pour ouvrir le modal */}
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Modal pour ajouter un événement */}
          <div
            className="modal fade none-border"
            id="event-modal"
            tabIndex="-1"
            aria-labelledby="event-modalLabel"
            aria-hidden="true"
          >
            <div className="modal-dialog">
              <div className="modal-content">
                <div className="modal-header">
                  <h4 className="modal-title">
                    <strong>Add New Event</strong>
                  </h4>{" "}
                  {/* Titre du modal */}
                </div>
                <div className="modal-body"></div>{" "}
                {/* Contenu vide pour ajouter des détails d'événement */}
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-default waves-effect"
                    data-bs-dismiss="modal"
                  >
                    Close {/* Bouton de fermeture */}
                  </button>
                  <button
                    type="button"
                    className="btn btn-success save-event waves-effect waves-light"
                  >
                    Create event {/* Bouton pour créer l'événement */}
                  </button>

                  <button
                    type="button"
                    className="btn btn-danger delete-event waves-effect waves-light"
                    data-bs-dismiss="modal"
                  >
                    Delete {/* Bouton pour supprimer l'événement */}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Modal pour ajouter une catégorie */}
          <div
            className="modal fade none-border"
            id="add-category"
            tabIndex="-1"
            aria-labelledby="add-categoryLabel"
            aria-hidden="true"
          >
            <div className="modal-dialog">
              <div className="modal-content">
                <div className="modal-header">
                  <h4 className="modal-title">
                    <strong>Add a category</strong>
                  </h4>{" "}
                  {/* Titre du modal pour ajouter une catégorie */}
                </div>
                <div className="modal-body">
                  <form>
                    <div className="row">
                      <div className="col-md-6 mb-3 mb-sm-0">
                        <label className="control-label form-label">
                          Category Name
                        </label>
                        <input
                          className="form-control form-white"
                          placeholder="Enter name"
                          type="text"
                          name="category-name"
                        />
                      </div>
                      <div className="col-md-6">
                        <label className="control-label form-label">
                          Choose Category Color
                        </label>
                        <select
                          className="form-control form-white"
                          data-placeholder="Choose a color..."
                          name="category-color"
                        >
                          <option value="success">Success</option>
                          <option value="danger">Danger</option>
                          <option value="info">Info</option>
                          <option value="pink">Pink</option>
                          <option value="primary">Primary</option>
                          <option value="warning">Warning</option>
                        </select>
                      </div>
                    </div>
                  </form>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-danger waves-effect"
                    data-bs-dismiss="modal"
                  >
                    Close {/* Bouton de fermeture du modal */}
                  </button>
                  <button
                    type="button"
                    className="btn btn-success waves-effect waves-light save-category"
                    data-bs-toggle="modal"
                  >
                    Save {/* Bouton pour enregistrer la catégorie */}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Fin du conteneur principal */}
    </div>
  );
};

export default Emploi;
