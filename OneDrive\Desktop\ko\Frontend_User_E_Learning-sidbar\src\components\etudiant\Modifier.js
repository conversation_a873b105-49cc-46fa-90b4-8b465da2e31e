import React, { useState } from "react";

const Modifier = () => {
  const [formData, setFormData] = useState({
    prenom: "<PERSON><PERSON><PERSON>",
    nom: "<PERSON><PERSON>",
    email: "<EMAIL>",
    niveau: "",
    abonnement: "",
    telephone: "+01 ************",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Données soumises:", formData);
  };

  return (
    <div className="content-body">
      <div className="container-fluid">
        <div className="row page-titles mx-0">
          <div className="col-sm-6 p-md-0">
            <div className="welcome-text">
              <h4>Modifier l'étudiant</h4>
            </div>
          </div>
          <div className="col-sm-6 p-md-0 justify-content-sm-end mt-2 mt-sm-0 d-flex">
            <ol className="breadcrumb">
              <li className="breadcrumb-item">
                <a href="#!">Étudiants</a>
              </li>
              <li className="breadcrumb-item active">
                <a href="#!">Modifier l'étudiant</a>
              </li>
            </ol>
          </div>
        </div>

        <div className="row">
          <div className="col-xl-12 col-xxl-12 col-sm-12">
            <div className="card">
              <div className="card-header">
                <h5 className="card-title">Informations de base</h5>
              </div>
              <div className="card-body">
                <form onSubmit={handleSubmit}>
                  <div className="row">
                    <div className="col-sm-6">
                      <div className="form-group">
                        <label htmlFor="prenom">Prénom</label>
                        <input
                          id="prenom"
                          name="prenom"
                          type="text"
                          className="form-control"
                          value={formData.prenom}
                          onChange={handleChange}
                          required
                        />
                      </div>
                    </div>

                    <div className="col-sm-6">
                      <div className="form-group">
                        <label htmlFor="nom">Nom</label>
                        <input
                          id="nom"
                          name="nom"
                          type="text"
                          className="form-control"
                          value={formData.nom}
                          onChange={handleChange}
                          required
                        />
                      </div>
                    </div>

                    <div className="col-sm-6">
                      <div className="form-group">
                        <label htmlFor="email">Email</label>
                        <input
                          id="email"
                          name="email"
                          type="email"
                          className="form-control"
                          value={formData.email}
                          onChange={handleChange}
                          required
                        />
                      </div>
                    </div>

                    <div className="col-sm-6">
                      <div className="form-group">
                        <label htmlFor="niveau">Niveau d'étude</label>
                        <select
                          id="niveau"
                          name="niveau"
                          className="form-control"
                          value={formData.niveau}
                          onChange={handleChange}
                          required
                        >
                          <option value="">Sélectionner votre niveau</option>
                          <option value="niveau 1">Niveau 1</option>
                          <option value="niveau 2">Niveau 2</option>
                          <option value="niveau 3">Niveau 3</option>
                        </select>
                      </div>
                    </div>

                    <div className="col-sm-6">
                      <div className="form-group">
                        <label htmlFor="abonnement">Abonnement</label>
                        <select
                          id="abonnement"
                          name="abonnement"
                          className="form-control"
                          value={formData.abonnement}
                          onChange={handleChange}
                          required
                        >
                          <option value="">Sélectionner un abonnement</option>
                          <option value="Abonnement 1">Abonnement 1</option>
                          <option value="Abonnement 2">Abonnement 2</option>
                          <option value="Abonnement 3">Abonnement 3</option>
                        </select>
                      </div>
                    </div>

                    <div className="col-sm-6">
                      <div className="form-group">
                        <label htmlFor="telephone">Numéro de téléphone</label>
                        <input
                          id="telephone"
                          name="telephone"
                          type="tel"
                          className="form-control"
                          value={formData.telephone}
                          onChange={handleChange}
                          required
                        />
                      </div>
                    </div>

                    <div className="col-lg-12 col-md-12 col-sm-12">
                      <button
                        type="submit"
                        className=" btn-primary mt-2  btn-sm"
                        style={{ backgroundColor: " #F2BC00", color: "white" }}
                      >
                        Modifier
                      </button>
                      <button type="button" className="btn btn-light">
                        Annuler
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Modifier;
