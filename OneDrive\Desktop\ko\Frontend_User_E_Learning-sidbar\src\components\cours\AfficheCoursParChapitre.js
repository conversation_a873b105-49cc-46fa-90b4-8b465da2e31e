import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Modal, Form, Pagination } from "react-bootstrap";
import axiosInstance from "../../services/axiosService";
import { useKeycloak } from "@react-keycloak/web";
import keycloak from "../../keycloak";

const AfficheCoursParChapitre = () => {
  const { keycloak } = useKeycloak();
  const { idMatiere, id } = useParams();
  const navigate = useNavigate();

  const [cours, setCours] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [selectedCours, setSelectedCours] = useState(null);
  const [formData, setFormData] = useState({
    titre: "",
    description: "",
    lien: "",
    pdf: "",
    dateCreation: "",
    duree: "",
  });
  const [searchTerm, setSearchTerm] = useState("");

  // Récupération des rôles Keycloak
  const [roles, setRoles] = useState([]);
  const [isAdminOrEnseignant, setIsAdminOrEnseignant] = useState(false);

  useEffect(() => {
    if (keycloak.authenticated) {
      const userRoles = keycloak.realmAccess?.roles || [];
      setRoles(userRoles);
      setIsAdminOrEnseignant(
        userRoles.includes("admin") || userRoles.includes("enseignant")
      );
      console.log("Roles chargés:", userRoles);
    }
  }, [keycloak, keycloak.authenticated]);

  // Filtrage des cours
  const isSearching = searchTerm.length > 0;
  const filteredCours = isSearching
    ? cours.filter((c) =>
        c.titre.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : cours;

  // Chargement des cours
  useEffect(() => {
    const fetchCours = async () => {
      try {
        const response = await axiosInstance.get(`/api/cours/chapitre/${id}`);
        if (Array.isArray(response.data)) {
          setCours(response.data);
        } else {
          setError("La réponse de l'API est invalide");
        }
      } catch (error) {
        setError(error.response?.data?.message || "Erreur de chargement");
      } finally {
        setLoading(false);
      }
    };

    fetchCours();
  }, [id]);

  // Gestion de la suppression
  const handleDelete = async (coursId) => {
    if (!window.confirm("Êtes-vous sûr de vouloir supprimer ce cours ?"))
      return;

    try {
      await axiosInstance.delete(`/api/cours/${coursId}`);
      setCours(cours.filter((c) => c.idCours !== coursId));
      setShowModal(false);
    } catch (error) {
      setError(
        error.response?.data?.message || "Erreur lors de la suppression"
      );
    }
  };

  // Gestion de la modification
  const handleEdit = async () => {
    if (!selectedCours) return;

    try {
      const updatedCours = {
        idCours: selectedCours.idCours,
        ...formData,
        chapitre: selectedCours.chapitre,
      };

      const response = await axiosInstance.put(
        `/api/cours/${selectedCours.idCours}`,
        updatedCours
      );

      setCours((prev) =>
        prev.map((c) =>
          c.idCours === selectedCours.idCours ? response.data : c
        )
      );
      setShowModal(false);
    } catch (error) {
      setError(
        error.response?.data?.message || "Erreur lors de la modification"
      );
    }
  };

  // Gestion des changements de formulaire
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Ouverture du modal d'édition
  const handleShowModal = (cours) => {
    setSelectedCours(cours);
    setFormData({
      titre: cours.titre,
      description: cours.description,
      lien: cours.lien,
      pdf: cours.pdf,
      dateCreation: cours.dateCreation?.split("T")[0],
      duree: cours.duree,
    });
    setShowModal(true);
  };

  // Formatage de date
  const formatDate = (dateStr) => {
    return new Date(dateStr).toLocaleDateString("fr-FR");
  };

  // Affichage des liens vidéo
  const renderVideoLink = (lien) => {
    if (!lien) return null;

    const match = lien.match(/(?:vimeo\.com\/|\/videos\/|video\/)?(\d+)/);
    if (match) {
      return (
        <div className="embed-responsive embed-responsive-16by9 mb-3">
          <iframe
            src={`https://player.vimeo.com/video/${match[1]}`}
            width="100%"
            height="200"
            frameBorder="0"
            allow="autoplay; fullscreen"
            allowFullScreen
            title="Vidéo du cours"
          ></iframe>
        </div>
      );
    }

    return (
      <a
        href={lien}
        target="_blank"
        rel="noopener noreferrer"
        className="btn btn-primary btn-sm"
      >
        Voir la vidéo
      </a>
    );
  };

  if (loading)
    return <div className="text-center my-5">Chargement en cours...</div>;
  if (error)
    return <div className="alert alert-danger text-center my-5">{error}</div>;

  return (
    <div className="container-fluid">
      {/* En-tête */}
      <div className="row page-titles mx-0 d-flex align-items-center justify-content-between">
        <div className="col-auto">
          <h4 style={{ color: "#37A7DF" }}>Tous Les cours</h4>
          <p>Total: {filteredCours.length} cours</p>
        </div>

        <div className="col-md-4">
          <Form.Control
            type="text"
            placeholder="Rechercher un cours par titre..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {keycloak.hasRealmRole("ADMIN", "ENSEIGNANT") && (
          <div className="col-auto">
            <button
              className="btn btn-primary"
              onClick={() => navigate("/cours/ajouter")}
              style={{ backgroundColor: "#37A7DF", borderColor: "#37A7DF" }}
            >
              + Ajouter un cours
            </button>
          </div>
        )}
      </div>

      {/* Liste des cours */}
      <div className="row">
        {filteredCours.length > 0 ? (
          filteredCours.map((cours) => (
            <div
              key={cours.idCours}
              className="col-xl-3 col-lg-4 col-md-6 mb-4"
            >
              <div className="card h-100">
                <div className="card-body">
                  <h5 className="card-title">{cours.titre}</h5>
                  {renderVideoLink(cours.lien)}
                  <p className="card-text">
                    <strong>Description:</strong> {cours.description}
                  </p>
                  <p className="card-text">
                    <strong>Durée:</strong> {cours.duree} minutes
                  </p>
                  <p className="card-text">
                    <strong>Date:</strong> {formatDate(cours.dateCreation)}
                  </p>
                  <p className="card-text">
                    <strong>Chapitre:</strong>{" "}
                    {cours.chapitre?.nomChapitre || "N/A"}
                  </p>
                  {cours.pdf && (
                    <a
                      href={cours.pdf}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="btn btn-info btn-sm mb-2 me-2"
                    >
                      Voir le PDF
                    </a>
                  )}

                  {/* Bouton pour voir le cours en mode leçon (pour tous les utilisateurs) */}
                  <button
                    onClick={() => navigate(`/lesson/${id}/${cours.idCours}`)}
                    className="btn btn-success btn-sm mb-2 w-100"
                    style={{
                      backgroundColor: "#248E39",
                      borderColor: "#248E39",
                    }}
                  >
                    Voir le cours
                  </button>

                  {/* Boutons d'action seulement pour admin/enseignant */}
                  {keycloak.hasRealmRole("ADMIN", "ENSEIGNANT") && (
                    <div className="d-flex justify-content-between mt-3">
                      <button
                        onClick={() => handleShowModal(cours)}
                        className="btn btn-warning btn-sm"
                      >
                        Modifier
                      </button>
                      <button
                        onClick={() => handleDelete(cours.idCours)}
                        className="btn btn-danger btn-sm"
                      >
                        Supprimer
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="col-12 text-center my-5">
            <p>Aucun cours trouvé</p>
          </div>
        )}
      </div>

      {/* Modal d'édition (seulement pour admin/enseignant) */}
      {isAdminOrEnseignant && (
        <Modal show={showModal} onHide={() => setShowModal(false)}>
          <Modal.Header closeButton>
            <Modal.Title>Modifier le cours</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form>
              <Form.Group className="mb-3">
                <Form.Label>Titre</Form.Label>
                <Form.Control
                  name="titre"
                  value={formData.titre}
                  onChange={handleInputChange}
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Description</Form.Label>
                <Form.Control
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  as="textarea"
                  rows={3}
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Durée (minutes)</Form.Label>
                <Form.Control
                  type="number"
                  name="duree"
                  value={formData.duree}
                  onChange={handleInputChange}
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Date de création</Form.Label>
                <Form.Control
                  type="date"
                  name="dateCreation"
                  value={formData.dateCreation}
                  onChange={handleInputChange}
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Lien PDF</Form.Label>
                <Form.Control
                  name="pdf"
                  value={formData.pdf}
                  onChange={handleInputChange}
                  placeholder="URL du PDF"
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Lien vidéo</Form.Label>
                <Form.Control
                  name="lien"
                  value={formData.lien}
                  onChange={handleInputChange}
                  placeholder="URL de la vidéo"
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <button
              className="btn btn-secondary"
              onClick={() => setShowModal(false)}
            >
              Annuler
            </button>
            <button className="btn btn-primary" onClick={handleEdit}>
              Sauvegarder
            </button>
          </Modal.Footer>
        </Modal>
      )}
    </div>
  );
};

export default AfficheCoursParChapitre;
