import React, { useEffect, useRef } from "react";
import "./Login/login.css"; // Ensure necessary styles are defined
import HeaderImage from "../images/deutzaaa_blanc__1_-removebg-preview.png"; // Chemin relatif correct
import { useKeycloak } from '@react-keycloak/web';
import logo from "../images/logo1.png";
import 'bootstrap/dist/js/bootstrap.bundle.min'; // Obligatoire pour les dropdowns
import '../styles/responsive.css';
import '../styles/theme.css';

const Header = ({ toggleSidebar }) => {
  const { keycloak } = useKeycloak(); // Use hook to get keycloak
  const dropdownRef = useRef(null);
  const dropdownInstance = useRef(null);

  useEffect(() => {
    // Initialize Bootstrap dropdown
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      const bootstrap = require('bootstrap/dist/js/bootstrap.bundle.min');

      // Check if dropdown element exists
      if (dropdownRef.current) {
        dropdownInstance.current = new bootstrap.Dropdown(dropdownRef.current);
      }
    }

    // Cleanup on unmount
    return () => {
      if (dropdownInstance.current && typeof dropdownInstance.current.dispose === 'function') {
        dropdownInstance.current.dispose();
      }
    };
  }, []);

  const toggleDropdown = () => {
    if (dropdownInstance.current) {
      dropdownInstance.current.toggle();
    }
  };

  return (
    <header className="header shadow-sm d-flex align-items-center justify-content-between px-4 py-2" style={{ backgroundColor: 'var(--header-bg)' }}>
      {/* Mobile menu toggle button */}
      <div className="d-mobile-block d-lg-none">
        <button
          className="nav-control"
          onClick={toggleSidebar}
          style={{
            background: 'none',
            border: 'none',
            fontSize: '24px',
            marginRight: '15px',
            cursor: 'pointer',
            color: 'var(--primary-blue)'
          }}
        >
          <i className="la la-bars"></i>
        </button>
      </div>

      {/* Main container of the header */}
      <div className="d-flex align-items-center">
        {/* Logo aligned to the left */}
        <a href="/" className="brand-logo d-flex align-items-center me-3">
          <img
            src={HeaderImage}
            alt="Logo"
            className="logo-image"
            style={{ width: "90px", height: "auto" }}
          />
        </a>
        {/* Search bar to the left */}
        <div className="header-left d-none d-md-block">
          <div className="search_bar dropdown">
            <span
              className="search_icon p-3 c-pointer"
              data-bs-toggle="dropdown"
            >
              <i className="mdi mdi-magnify"></i>
            </span>
            <div className="dropdown-menu p-0 m-0">
              <form>
                <input
                  className="form-control"
                  type="search"
                  placeholder="Search"
                  aria-label="Search"
                />
              </form>
            </div>
          </div>
        </div>
      </div>

      {/* Menu on the right */}
      <nav className="navbar navbar-expand">
        <div className="collapse navbar-collapse justify-content-end">
          <ul className="navbar-nav header-right">
            {/* Authentication with Keycloak */}
            <li className="nav-item dropdown header-profile">
  <button
    className="nav-link dropdown-toggle"
    type="button"
    id="profileDropdown"
    ref={dropdownRef}
    onClick={toggleDropdown}
    aria-expanded="false"
    style={{
      cursor: 'pointer',
      color: 'var(--primary-blue)',
      display: 'flex',
      alignItems: 'center',
      padding: '8px 15px',
      borderRadius: '4px',
      transition: 'all 0.3s ease',
      background: 'none',
      border: 'none'
    }}
  >
    <img src={logo} width="24" alt="User Logo" className="me-2" style={{ borderRadius: '50%' }} />
    <span style={{ fontWeight: '500' }}>
      {keycloak.authenticated
        ? keycloak.tokenParsed.preferred_username
        : 'Login'}
    </span>
  </button>

  <ul className="dropdown-menu dropdown-menu-end shadow" aria-labelledby="profileDropdown"
      style={{
        borderRadius: '8px',
        border: 'none',
        marginTop: '10px',
        overflow: 'hidden'
      }}>
    {!keycloak.authenticated ? (
      <li>
        <button
          className="dropdown-item"
          onClick={() => keycloak.login()}
          style={{
            padding: '10px 20px',
            color: 'var(--text-primary)',
            transition: 'all 0.2s ease',
            background: 'none',
            border: 'none',
            textAlign: 'left',
            width: '100%'
          }}
        >
          <i className="la la-sign-in-alt me-2"></i> Login
        </button>
      </li>
    ) : (
      <>
        <li>
          <button
            className="dropdown-item"
            style={{
              padding: '10px 20px',
              color: 'var(--text-primary)',
              transition: 'all 0.2s ease',
              background: 'none',
              border: 'none',
              textAlign: 'left',
              width: '100%'
            }}
          >
            <i className="la la-user me-2"></i> Profile
          </button>
        </li>
        <li>
          <button
            className="dropdown-item"
            onClick={() => keycloak.logout()}
            style={{
              padding: '10px 20px',
              color: 'var(--text-primary)',
              transition: 'all 0.2s ease',
              background: 'none',
              border: 'none',
              textAlign: 'left',
              width: '100%'
            }}
          >
            <i className="la la-sign-out-alt me-2"></i> Logout
          </button>
        </li>
      </>
    )}
  </ul>
</li>

{/**
 *
 */}
{/**<li class="nav-item dropdown header-profile">
								<a class="nav-link"  role="button" data-bs-toggle="dropdown">
									<img src="images/profile/education/pic1.jpg" width="20" alt="" />
								</a>
								<div class="dropdown-menu dropdown-menu-right">
									<a class="dropdown-item ai-icon">

										<span class="ms-2">Profile </span>
									</a>

									<a href="./page-login.html" class="dropdown-item ai-icon">

										<span class="ms-2">Logout </span>
									</a>
								</div>
							</li> */}
            {/* Add more items like profile or notifications */}
          </ul>
        </div>
      </nav>
    </header>
  );
};

export default Header;