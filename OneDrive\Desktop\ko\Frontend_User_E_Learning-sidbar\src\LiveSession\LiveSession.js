import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { Card, Table, Badge, Button } from 'react-bootstrap';
import Swal from 'sweetalert2';
import keycloak from '../keycloak';

const LiveSessions = () => {
  const [liveSessions, setLiveSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);  const [userRole, setUserRole] = useState('');
  const [userId, setUserId] = useState('');

  const fetchLiveSessions = useCallback(async () => {
    try {
      if (!keycloak.authenticated) {
        setError('You must be logged in to view live sessions');
        setLoading(false);
        return;
      }

      setLoading(true);

      // Include the authentication token in the request
      const token = keycloak.token;
      const config = {
        headers: {
          'Authorization': `Bear<PERSON> ${token}`
        }
      };

      // We need to determine if the user is an ENSEIGNANT or not
      const isEnseignant = keycloak.hasResourceRole('ENSEIGNANT');
      let response;

      try {
        if (isEnseignant && userId) {
          // Fetch only teacher's sessions if the user is a teacher
          response = await axios.get(
            `http://localhost:8084/api/livesessions/enseignant/${userId}`,
            config
          );
        } else {            // For students, use the correct student-specific endpoint
          try {
            response = await axios.get(
              'http://localhost:8084/api/livesessions/student/available',
              config
            );
          } catch (error) {
            // Fallback to general endpoint if student endpoint doesn't exist
            response = await axios.get(
              'http://localhost:8084/api/livesessions',
              config
            );
          }
        }

        setLiveSessions(response.data);
        setError(null);
      } catch (err) {
        console.error('Error fetching live sessions:', err);
        // Set empty sessions array instead of showing error to students
        setLiveSessions([]);
        setError('No live sessions available at the moment.');
      }
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        // Use Keycloak for user info instead of local storage
        if (keycloak.authenticated) {
          // Check if user has the ENSEIGNANT role
          const isEnseignant = keycloak.hasResourceRole('ENSEIGNANT');
          setUserRole(isEnseignant ? 'ROLE_ENSEIGNANT' : 'ROLE_ETUDIANT');
          
          // Only try to get the enseignant ID if user is an enseignant
          if (isEnseignant) {
            // We'll need to get the numeric ID for the current user
            const token = keycloak.token;
            const config = {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            };
            
            try {
              // Get all enseignants and find the current one by email/username
              const enseignantsResponse = await axios.get(
                `http://localhost:8084/api/enseignants`, 
                config
              );
              
              const username = keycloak.tokenParsed.preferred_username;
              const email = keycloak.tokenParsed.email;
              
              const currentUser = enseignantsResponse.data.find(
                e => e.username === username || e.email === email
              );
              
              if (currentUser) {
                setUserId(currentUser.id);
              }
            } catch (enseignantError) {
              console.log("Could not fetch enseignant data, user may not have permission");
              // Continue without setting userId
            }
          }
        }
      } catch (error) {
        console.error('Error fetching user info', error);
      }
    };

    fetchUserInfo();
  }, []);

  // Separate useEffect for fetching sessions when userId changes
  useEffect(() => {
    if (userId) {
      fetchLiveSessions();
    }
  }, [userId, fetchLiveSessions]);

  const handleStartSession = async (sessionId) => {
    try {
      // Include the authentication token
      const token = keycloak.token;
      const config = {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };
      
      await axios.post(`http://localhost:8084/api/livesessions/${sessionId}/start`, {}, config);
      
      Swal.fire({
        title: 'Success!',
        text: 'Live session started successfully',
        icon: 'success',
        confirmButtonText: 'OK'
      });
      
      // Refresh the list
      fetchLiveSessions();
    } catch (error) {
      Swal.fire({
        title: 'Error!',
        text: 'Failed to start the session',
        icon: 'error',
        confirmButtonText: 'OK'
      });
      console.error('Error starting session:', error);
    }
  };

  const handleEndSession = async (sessionId) => {
    try {
      await axios.post(`http://localhost:8084/api/livesessions/${sessionId}/end`);
      
      Swal.fire({
        title: 'Success!',
        text: 'Live session ended successfully',
        icon: 'success',
        confirmButtonText: 'OK'
      });
      
      // Refresh the list
      fetchLiveSessions();
    } catch (error) {
      Swal.fire({
        title: 'Error!',
        text: 'Failed to end the session',
        icon: 'error',
        confirmButtonText: 'OK'
      });
      console.error('Error ending session:', error);
    }
  };

  const handleCancelSession = async (sessionId) => {
    try {
      await axios.post(`http://localhost:8084/api/livesessions/${sessionId}/cancel`);
      
      Swal.fire({
        title: 'Success!',
        text: 'Session canceled successfully',
        icon: 'success',
        confirmButtonText: 'OK'
      });
      
      // Refresh the list
      fetchLiveSessions();
    } catch (error) {
      Swal.fire({
        title: 'Error!',
        text: 'Failed to cancel the session',
        icon: 'error',
        confirmButtonText: 'OK'
      });
      console.error('Error canceling session:', error);
    }
  };

  const handleDeleteSession = async (sessionId) => {
    try {
      const result = await Swal.fire({
        title: 'Êtes-vous sûr?',
        text: 'Cette action supprimera définitivement la session. Cette action ne peut pas être annulée.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Oui, supprimer!',
        cancelButtonText: 'Annuler'
      });

      if (result.isConfirmed) {
        const token = keycloak.token;
        const config = {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        };

        await axios.delete(`http://localhost:8084/api/livesessions/${sessionId}?enseignantId=${userId}`, config);

        Swal.fire({
          title: 'Supprimé!',
          text: 'La session a été supprimée avec succès.',
          icon: 'success',
          confirmButtonText: 'OK'
        });

        fetchLiveSessions();
      }
    } catch (error) {
      console.error('Error deleting session:', error);
      const errorMessage = error.response?.data?.message || 'Erreur lors de la suppression de la session';
      Swal.fire({
        title: 'Erreur!',
        text: errorMessage,
        icon: 'error',
        confirmButtonText: 'OK'
      });
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'SCHEDULED':
        return <Badge bg="warning">Scheduled</Badge>;
      case 'LIVE':
        return <Badge bg="success">Live</Badge>;
      case 'COMPLETED':
        return <Badge bg="info">Completed</Badge>;
      case 'CANCELLED':
        return <Badge bg="danger">Cancelled</Badge>;
      default:
        return <Badge bg="secondary">{status}</Badge>;
    }
  };

  const formatDateTime = (dateTimeStr) => {
    if (!dateTimeStr) return 'Not scheduled';
    const date = new Date(dateTimeStr);
    return date.toLocaleString();
  };

  if (loading) {
    return <div className="text-center py-5"><div className="spinner-border text-primary" role="status"></div></div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  return (
    
      <div className="container-fluid">
        <div className="row page-titles mx-0">
          <div className="col-sm-6 p-md-0">
            <div className="welcome-text">
              <h4>Live Sessions</h4>
              <p className="mb-0">Manage and join live teaching sessions</p>
            </div>
          </div>
          <div className="col-sm-6 p-md-0 justify-content-sm-end mt-2 mt-sm-0 d-flex">
            {userRole === 'ROLE_ENSEIGNANT' && (
              <Link to="/live-sessions/create" className="btn btn-primary">
                Create New Session
              </Link>
            )}
          </div>
        </div>

        <div className="row">
          <div className="col-lg-12">
            <Card>
              <Card.Body>
                <div className="table-responsive">
                  <Table className="table table-hover">
                    <thead>
                      <tr>
                        <th>Title</th>
                        <th>Subject</th>
                        <th>Scheduled Start</th>
                        <th>Scheduled End</th>
                        <th>Status</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {liveSessions.length === 0 ? (
                        <tr>
                          <td colSpan="6" className="text-center">No live sessions found</td>
                        </tr>
                      ) : (
                        liveSessions.map(session => (
                          <tr key={session.id}>
                            <td>{session.title}</td>
                            <td>{session.matiere ? session.matiere.nomMatiere : 'General'}</td>
                            <td>{formatDateTime(session.scheduledStartTime)}</td>
                            <td>{formatDateTime(session.scheduledEndTime)}</td>
                            <td>{getStatusBadge(session.status)}</td>
                            <td>
                              <div className="d-flex">
                                {userRole === 'ROLE_ENSEIGNANT' ? (
                                  <>
                                    {session.status === 'SCHEDULED' && (
                                      <>
                                        <Button variant="success" className="mr-2" size="sm" onClick={() => handleStartSession(session.id)}>
                                          Start
                                        </Button>
                                        <Button variant="danger" className="mr-2" size="sm" onClick={() => handleCancelSession(session.id)}>
                                          Cancel
                                        </Button>
                                        <Link to={`/live-sessions/edit/${session.id}`} className="btn btn-warning btn-sm mr-2">
                                          Modifier
                                        </Link>
                                        <Button variant="outline-danger" className="mr-2" size="sm" onClick={() => handleDeleteSession(session.id)}>
                                          Supprimer
                                        </Button>
                                      </>
                                    )}
                                    {session.status === 'LIVE' && (
                                      <Button variant="danger" className="mr-2" size="sm" onClick={() => handleEndSession(session.id)}>
                                        End
                                      </Button>
                                    )}
                                    {(session.status === 'CANCELLED' || session.status === 'COMPLETED') && (
                                      <Button variant="outline-danger" className="mr-2" size="sm" onClick={() => handleDeleteSession(session.id)}>
                                        Supprimer
                                      </Button>
                                    )}
                                  </>
                                ) : null}

                                {(session.status === 'SCHEDULED' || session.status === 'LIVE') && (
                                  <Link to={`/live-sessions/join/${session.id}`} className="btn btn-info btn-sm">
                                    Join
                                  </Link>
                                )}
                              </div>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </Table>
                </div>
              </Card.Body>
            </Card>
          </div>
        </div>
      </div>
    
  );
};

export default LiveSessions;
