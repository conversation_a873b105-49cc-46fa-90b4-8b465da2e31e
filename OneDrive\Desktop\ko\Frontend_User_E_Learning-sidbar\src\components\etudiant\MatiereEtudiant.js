import React, { useState, useEffect } from "react";
import { Form } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import axiosInstance from "../../services/axiosService";
import keycloak from "../../keycloak";

const MatiereEtudiant = () => {
  const navigate = useNavigate();
  const [userId, setUserId] = useState(null);
  const [matieres, setMatieres] = useState([]);
  const [imageUrls, setImageUrls] = useState({});
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [successMessage, setSuccessMessage] = useState("");
  const matieresPerPage = 8;

  // Fonction de chargement d’image
  const loadImage = async (imageId) => {
    try {
      // Update to use axiosInstance with proper auth
      const response = await axiosInstance.get(`/api/image/load/${imageId}`, {
        responseType: "blob",
        headers: {
          Accept: "image/*",
        },
      });
      const blobUrl = URL.createObjectURL(new Blob([response.data]));
      setImageUrls((prev) => ({ ...prev, [imageId]: blobUrl }));
    } catch (error) {
      console.error("Error loading image:", error);
      // Keep the old URL if there's an error
      setImageUrls((prev) => ({
        ...prev,
        [imageId]: prev[imageId] || "placeholder-image.jpg",
      }));
    }
  };

  useEffect(() => {
    // Cleanup function to revoke blob URLs
    return () => {
      Object.values(imageUrls).forEach((url) => URL.revokeObjectURL(url));
    };
  }, [imageUrls]);

  // Nettoyer les blobs à la destruction du composant
  useEffect(() => {
    return () => {
      Object.values(imageUrls).forEach((url) => {
        if (url && url.startsWith("blob:")) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, [imageUrls]);

  // Récupération de l'ID de l'étudiant
  useEffect(() => {
    const fetchUserId = async () => {
      try {
        const response = await axiosInstance.get("/api/etudiants");
        const username = keycloak.tokenParsed?.preferred_username;
        const email = keycloak.tokenParsed?.email;

        const etudiant = response.data.find(
          (e) => e.username === username || e.email === email
        );

        if (etudiant) {
          setUserId(etudiant.id || etudiant.idEtudiant);
        } else {
          setError("Étudiant introuvable.");
        }
      } catch (err) {
        setError("Erreur lors de la récupération de l'étudiant.");
      }
    };

    if (keycloak.authenticated) fetchUserId();
  }, []);

  // Récupération des matières
  useEffect(() => {
    const fetchMatieres = async () => {
      if (!userId) return;

      try {
        setLoading(true);
        // Récupérer les matières avec leurs abonnements et niveaux
        const matieresRes = await axiosInstance.get(`/api/etudiants/${userId}/abonnements-matieres`);
        const allMatieres = matieresRes.data.content || matieresRes.data;
        console.log("Données reçues :", matieresRes.data);

        // Pour chaque matière, récupérer les détails complets
        const matieresWithDetails = await Promise.all(
          allMatieres.map(async (matiere) => {
            try {
              // Récupérer les détails complets de la matière
              const matiereDetailRes = await axiosInstance.get(`/api/matieres/${matiere.idMatiere}`);
              return matiereDetailRes.data;
            } catch (error) {
              console.error(`Erreur lors de la récupération des détails de la matière ${matiere.idMatiere}:`, error);
              return matiere;
            }
          })
        );

        console.log("Matières avec détails:", matieresWithDetails);
        setMatieres(matieresWithDetails);
      } catch (err) {
        console.error(err);
        setError("Erreur lors du chargement des matières.");
      } finally {
        setLoading(false);
      }
    };

    fetchMatieres();
  }, [userId]);

  console.log("Matieres à afficher : ", matieres);
  console.log("URLs des images : ", imageUrls);

  // Filtrage
  const matieresFiltres = matieres.filter((matiere) =>
    matiere.nomMatiere?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    matiere.niveau?.nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    matiere.duree?.toString().includes(searchTerm)
  );

  const totalPages = Math.ceil(matieresFiltres.length / matieresPerPage);
  const currentMatieres = matieresFiltres.slice(
    (currentPage - 1) * matieresPerPage,
    currentPage * matieresPerPage
  );

  const goToNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1);
  };

  const goToPrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

  return (
    <div className="container-fluid">
      <div className="row page-titles mx-0 d-flex align-items-center justify-content-between">
        <div className="col-auto">
          <h4 style={{ color: "#37A7DF" }}>Mes Matières</h4>
        </div>
        <div className="col-md-4">
          <Form.Control
            type="text"
            placeholder="Rechercher une matière..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {error && <div className="alert alert-danger">{error}</div>}
      {successMessage && <div className="alert alert-success">{successMessage}</div>}
      {loading && <div className="text-center">Chargement...</div>}

      <div className="row">
        {currentMatieres.length === 0 ? (
          <p>Aucune matière trouvée.</p>
        ) : (
          currentMatieres.map((matiere) => (
            <div key={matiere.idMatiere} className="col-xl-3 col-lg-4 col-md-6 col-sm-6">
              <div className="card">
                    {matiere.image ? (
                      <img
                        className="card-img-top"
                        src={
                          imageUrls[matiere.image.idImage] ||
                          "placeholder-image.jpg"
                        }
                        alt={matiere.nomMatiere}
                        style={{ height: "150px", objectFit: "cover" }}
                        onLoad={() =>
                          !imageUrls[matiere.image.idImage] &&
                          loadImage(matiere.image.idImage)
                        }
                        onError={(e) => {
                          // Retry loading once on error
                          if (!e.target.retryAttempted) {
                            e.target.retryAttempted = true;
                            loadImage(matiere.image.idImage);
                          }
                        }}
                      />
                    ) : (
                      <p className="text-muted text-center">Pas d'image</p>
                    )}

                <div className="card-body">
                  <h4>{matiere.nomMatiere || "Nom non défini"}</h4>
                  <p>{matiere.description || "Description non disponible"}</p>
                  <p>
                    <strong>Durée :</strong> {matiere.duree || "Non définie"}H
                  </p>
                  <p>
                    <strong>Abonnement :</strong>{" "}
                    {matiere.abonnements && matiere.abonnements.length > 0
                      ? matiere.abonnements.map(a => a.nom).join(", ")
                      : (matiere.abonnement?.nom || "Non défini")}
                  </p>
                  <p>
                    <strong>Niveau :</strong>{" "}
                    {matiere.matiereNiveaux && matiere.matiereNiveaux.length > 0
                      ? matiere.matiereNiveaux.map(mn => mn.niveau?.nom).filter(Boolean).join(", ")
                      : (matiere.niveau?.nom || "Non défini")}
                  </p>
                  <div>
                    <button
                      onClick={() =>
                        navigate(`/chapitres/matiere/${matiere.idMatiere}`)
                      }
                      className="btn btn-primary mt-2 w-100 btn-sm"
                      style={{
                        backgroundColor: "#37A7DF",
                        borderColor: "#37A7DF",
                      }}
                    >
                      Liste des chapitres
                    </button>

                    {/* Bouton pour accéder directement aux leçons */}
                    <button
                      onClick={() => {
                        // Récupérer directement les chapitres pour cette matière
                        axiosInstance.get(`/api/chapitres/matiere/${matiere.idMatiere}`)
                          .then(response => {
                            const chapitres = response.data;
                            if (chapitres && chapitres.length > 0) {
                              navigate(`/lesson/${chapitres[0].id}`);
                            } else {
                              alert("Aucun chapitre disponible pour cette matière.");
                            }
                          })
                          .catch(err => {
                            console.error("Erreur lors de la récupération des chapitres", err);
                            alert("Erreur lors de la récupération des chapitres. Veuillez réessayer plus tard.");
                          });
                      }}
                      className="btn mt-2 w-100 btn-sm"
                      style={{
                        backgroundColor: "#248E39",
                        borderColor: "#248E39",
                        color: "#fff",
                      }}
                    >
                      Commencer à apprendre
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      <div className="d-flex justify-content-center mt-3">
        <button
          className="btn btn-secondary mx-1"
          onClick={goToPrevPage}
          disabled={currentPage === 1}
        >
          Précédent
        </button>
        <span className="mx-2">
          Page {currentPage} sur {totalPages}
        </span>
        <button
          className="btn btn-secondary mx-1"
          onClick={goToNextPage}
          disabled={currentPage === totalPages}
        >
          Suivant
        </button>
      </div>
    </div>
  );
};

export default MatiereEtudiant;
