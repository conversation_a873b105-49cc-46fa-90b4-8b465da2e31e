import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useKeycloak } from '@react-keycloak/web';
import Header from './header';
import Sidebar from './sidebar';
import '../styles/responsive.css';

const routeRoleMap = {
  // Routes admin
  "/niveaux-etude": ["admin"],
  "/niveaux-etude/ajouter": ["admin"],
  "/enseignants": ["admin"],
  "/ajouter-enseignant": ["admin"],
  "/enseignants/:id": ["admin"],
  "/etudiants": ["admin"],
  "/ajouter-etudiant": ["admin"],
  "/type-abonnements": ["admin"],
  "/abonnements": ["admin"],
  "/abonnements/ajouter": ["admin"],
  "/affichage": ["admin"],

  // Routes enseignant
  "/mes-matieres": ["enseignant"],
  "/mes-niveaux": ["enseignant"],

  // Routes partagées
  "/cours": ["admin", "enseignant", "etudiant"],
  "/matieres": ["admin", "enseignant", "etudiant"],

  // Routes pour les chapitres
  "/chapitres": ["admin", "enseignant", "etudiant"],
  "/chapitres/matiere": ["admin", "enseignant", "etudiant"],

  // Routes pour ajouter/modifier des chapitres et cours - restreintes aux admin et enseignants
  "/AjoutChapParMatiere": ["admin", "enseignant"],
  "/AjoutCoursParMatiere": ["admin", "enseignant"],
  "/chapitres/ajouter": ["admin", "enseignant"],

  // Routes pour les leçons - accessibles à tous
  "/lesson": ["admin", "enseignant", "etudiant"],
  "/cours/chapitre": ["admin", "enseignant", "etudiant"],

  // Routes pour les étudiants
  "/Mes-matieresETUD": ["etudiant", "admin"],
  "/Mes-abonnement": ["etudiant", "admin"],

  // Routes pour le forum - accessibles à tous
  "/forum": ["admin", "enseignant", "etudiant"],
  "/forum/poser-question": ["admin", "enseignant", "etudiant"],
  "/forum/question": ["admin", "enseignant", "etudiant"],
  "/forum/mes-questions": ["admin", "enseignant", "etudiant"]
};

const PrivateRoute = ({ children }) => {
  const { keycloak, initialized } = useKeycloak();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
      if (window.innerWidth >= 992) {
        setSidebarOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  if (!initialized) {
    return <div>Loading...</div>;
  }

  if (!keycloak.authenticated) {
    // If not authenticated, redirect to login with dashboard as the target
    keycloak.login({ redirectUri: window.location.origin + '/dashboard' });
    return null; // Return null while redirecting
  }

  const token = keycloak.tokenParsed;
  const realmRoles = token?.realm_access?.roles || [];
  const clientRoles = token?.resource_access?.["frontend-client"]?.roles || [];

  // 🛠️ Standardiser les rôles en minuscule
  const userRoles = [...realmRoles, ...clientRoles].map(role => role.toLowerCase());

  console.log("Token parsed:", token);
  console.log("Realm roles:", realmRoles);
  console.log("Client roles:", clientRoles);
  console.log("userRoles (standardisés):", userRoles);

  const path = location.pathname;
  const matchedPath = Object.keys(routeRoleMap).find(route =>
    path.startsWith(route.split("/:")[0])
  );

  const allowedRoles = matchedPath ? routeRoleMap[matchedPath] : null;

  // Vérifier si l'utilisateur a le rôle "enseignant" (insensible à la casse)
  const isEnseignant = userRoles.some(role => role.toLowerCase() === "enseignant");
  console.log("L'utilisateur est-il un enseignant?", isEnseignant);
  console.log("Path actuel:", path);
  console.log("Rôles autorisés pour ce chemin:", allowedRoles);

  // Si l'utilisateur est un enseignant, lui donner accès aux routes d'ajout de chapitres et cours
  if (
    allowedRoles &&
    !userRoles.includes("admin") &&
    !allowedRoles.some(role => userRoles.some(userRole => userRole.toLowerCase() === role.toLowerCase()))
  ) {
    // Si c'est un enseignant et qu'il essaie d'accéder à une route d'ajout de chapitre ou cours, autoriser
    if (isEnseignant &&
        (path.includes("/AjoutChapParMatiere") ||
         path.includes("/AjoutCoursParMatiere") ||
         path.includes("/chapitres/ajouter") ||
         path.includes("/chapitres/matiere"))) {
      console.log("Enseignant autorisé à accéder à:", path);
      // Autoriser l'accès
    } else {
      console.log("Accès refusé à:", path);
      return <Navigate to="/unauthorized" />;
    }
  }

  return (
    <div id="main-wrapper" className={`show ${sidebarOpen ? 'sidebar-open' : ''}`}>
      <Header toggleSidebar={toggleSidebar} />
      <Sidebar isOpen={sidebarOpen} toggleSidebar={toggleSidebar} />
      <div className="content-body">
        <div className="container-fluid">{children}</div>
      </div>
    </div>
  );
};

export default PrivateRoute;
