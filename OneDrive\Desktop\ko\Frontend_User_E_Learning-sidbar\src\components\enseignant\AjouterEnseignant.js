import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import axiosInstance from "../../services/axiosService";
import Select from "react-select";
import "./Enseignant.css";

// Fonction pour générer un mot de passe aléatoire
const generatePassword = (length = 10) => {
  const charset =
    "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+";
  let password = "";
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }
  return password;
};

const AjouterEnseignant = () => {
  const navigate = useNavigate();
  
  const [formData, setFormData] = useState({
    username: "",
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "", // Ajout du champ pour le numéro de téléphone
    password: generatePassword(12), // Génère un mot de passe aléatoire de 12 caractères
    niveauIds: [],
    matiereIds: [],
  });
  const customStyles = {
    control: (provided) => ({
      ...provided,
      borderRadius: "8px",
      borderColor: "#D1D5DB",
      boxShadow: "none",
      padding: "2px 4px",
      "&:hover": {
        borderColor: "#2563EB"
      }
    }),
  };

  // État pour stocker le mot de passe généré
  const [generatedPassword, setGeneratedPassword] = useState("");

  const [niveaux, setNiveaux] = useState([]);
  const [matieres, setMatieres] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [niveauxResponse, matieresResponse] = await Promise.all([
          axiosInstance.get("/api/niveaux/all"),
          axiosInstance.get("/api/matieres"),
        ]);
        setNiveaux(niveauxResponse.data);
        setMatieres(matieresResponse.data);

        // Stocker le mot de passe généré
        setGeneratedPassword(formData.password);
      } catch (err) {
        setError("Erreur lors du chargement des données");
        console.error(err);
      }
    };
    fetchData();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // La fonction handleMultiSelect a été remplacée par handleSelectChange
  const handleSelectChange = (selectedOptions, field) => {
    const selectedValues = selectedOptions.map((option) => option.value);
    setFormData({ ...formData, [field]: selectedValues });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation des champs obligatoires
    if (!formData.username) {
      setError("Le nom d'utilisateur est obligatoire");
      return;
    }

    if (!formData.email) {
      setError(
        "L'email est obligatoire pour envoyer les identifiants de connexion"
      );
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Afficher les données envoyées au backend pour le débogage
      console.log("Données envoyées au backend:", formData);

      await axiosInstance.post("/api/enseignants", formData);
      setSuccess(true);
      // Générer un nouveau mot de passe pour le prochain enseignant
      const newPassword = generatePassword(12);

      setFormData({
        username: "",
        firstName: "",
        lastName: "",
        email: "",
        phoneNumber: "", // Réinitialisation du numéro de téléphone
        password: newPassword,
        niveauIds: [],
        matiereIds: [],
      });

      // Stocker le nouveau mot de passe généré
      setGeneratedPassword(newPassword);
      setTimeout(() => {
        navigate("/enseignants");
      }, 2000);
    } catch (err) {
      setError(
        err.response?.data || "Erreur lors de la création de l'enseignant"
      );
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container-fluid">
      <div className="row justify-content-center">
        {/**<div className="col-sm-6 p-md-0"> */}
        <div className="col-lg-10 col-md-12">
          <div className="row page-titles mx-0 mb-4">
            <div className="col">
              {" "}
              <h4 className="fw-bold mb-1" style={{ color: "#37A7DF", fontSize: "24px" }}>
  Ajouter un enseignant
</h4>
<p style={{ color: "#6B7280", fontSize: "16px" }}>
  Créez un nouveau compte enseignant dans le système.
</p>
            </div>
          </div>
        </div>
        </div>

        <div className="row justify-content-center mb-4">
        <div className="col-lg-10 col-md-12">
            <div
              className="card"
              style={{
                boxShadow: "0 4px 10px rgba(0,0,0,0.1)",
                border: "none",
                borderRadius: "10px",
              }}
            >
              <div className="card-header py-3 px-4" style={{
  backgroundColor: "#EFF6FF",
  borderBottom: "1px solid #DBEAFE",
  borderTopLeftRadius: "10px",
  borderTopRightRadius: "10px"
}}>
  <h5 className="mb-0" style={{ color: "#37A7DF", fontWeight: 600 }}>
    📝 Informations de l'enseignant
  </h5>
</div>
              <div className="card-body" style={{ padding: "25px" }}>
                {error && (
                  <div
                    className="alert alert-danger"
                    role="alert"
                    style={{
                      backgroundColor: "#fff",
                      borderLeft: "4px solid #dc3545",
                      borderRadius: "0 4px 4px 0",
                      padding: "15px",
                    }}
                  >
                    <i
                      className="fa fa-exclamation-circle me-2"
                      style={{ color: "#dc3545" }}
                    ></i>
                    {error}
                  </div>
                )}
                {success && (
                  <div
                    className="alert alert-success"
                    role="alert"
                    style={{
                      backgroundColor: "#fff",
                      borderLeft: "4px solid #248E39",
                      borderRadius: "0 4px 4px 0",
                      padding: "15px",
                    }}
                  >
                    <i
                      className="fa fa-check-circle me-2"
                      style={{ color: "#248E39" }}
                    ></i>
                    <p style={{ margin: "0 0 10px 0" }}>
                      Enseignant créé avec succès!
                    </p>
                    <p style={{ margin: "0" }}>
                      Un email contenant les identifiants de connexion a été
                      envoyé à l'adresse :{" "}
                      <strong style={{ color: "#000080" }}>
                        {formData.email}
                      </strong>
                    </p>
                  </div>
                )}
                <form onSubmit={handleSubmit}>
                  <div className="row">
                    <div className="col-lg-6 mb-3">
                      <div className="form-group">
                        <label
                          className="text-label"
                          style={{
                            color: "#1D1D1B",
                            fontWeight: "500",
                            marginBottom: "8px",
                            display: "block",
                          }}
                        >
                          Login de l'enseignant{" "}
                          <span
                            style={{ color: "#F2BC00", fontWeight: "bold" }}
                          >
                            *
                          </span>
                        </label>
                        <input
                          type="text"
                          name="username"
                          className="form-control"
                          placeholder="Entrez le login"
                          value={formData.username}
                          onChange={handleChange}
                          required
                          style={{
                            borderColor: "#D1D5DB",
                            borderRadius: "8px",
                            padding: "10px",
                            backgroundColor: "#FFFFFF",
                            boxShadow: "none",
                            outline: "none",
                            transition: "border-color 0.2s ease-in-out"
                          }}
                          onFocus={(e) => (e.target.style.borderColor = "#2563EB")}
                          onBlur={(e) => (e.target.style.borderColor = "#D1D5DB")}
                        />
                      </div>
                    </div>
                    <div className="col-lg-6 mb-3">
                      <div className="form-group">
                        <label
                          className="text-label"
                          style={{
                            color: "#1D1D1B",
                            fontWeight: "500",
                            marginBottom: "8px",
                            display: "block",
                          }}
                        >
                          Mot de passe
                        </label>
                        <div
                          className="alert alert-info"
                          style={{
                            backgroundColor: "#EEF9F5",
                            borderLeft: "4px solid #37A7DF",
                            borderRadius: "0 4px 4px 0",
                            padding: "12px",
                            margin: "0",
                          }}
                        >
                          <i
                            className="la la-info-circle me-2"
                            style={{ color: "#37A7DF" }}
                          ></i>
                          Le mot de passe sera généré automatiquement et envoyé
                          par email à l'enseignant.
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-6 mb-3">
                      <div className="form-group">
                        <label
                          className="text-label"
                          style={{
                            color: "#1D1D1B",
                            fontWeight: "500",
                            marginBottom: "8px",
                            display: "block",
                          }}
                        >
                          Prénom{" "}
                          <span
                            style={{ color: "#F2BC00", fontWeight: "bold" }}
                          >
                            *
                          </span>
                        </label>
                        <input
                          type="text"
                          name="firstName"
                          className="form-control"
                          placeholder="Prénom de l'enseignant"
                          value={formData.firstName}
                          onChange={handleChange}
                          required
                          style={{
                            borderColor: "#D1D5DB",
                            borderRadius: "8px",
                            padding: "10px",
                            backgroundColor: "#FFFFFF",
                            boxShadow: "none",
                            outline: "none",
                            transition: "border-color 0.2s ease-in-out"
                          }}
                          onFocus={(e) => (e.target.style.borderColor = "#2563EB")}
                          onBlur={(e) => (e.target.style.borderColor = "#D1D5DB")}
                        />
                      </div>
                    </div>
                    <div className="col-lg-6 mb-3">
                      <div className="form-group">
                        <label
                          className="text-label"
                          style={{
                            color: "#1D1D1B",
                            fontWeight: "500",
                            marginBottom: "8px",
                            display: "block",
                          }}
                        >
                          Nom{" "}
                          <span
                            style={{ color: "#F2BC00", fontWeight: "bold" }}
                          >
                            *
                          </span>
                        </label>
                        <input
                          type="text"
                          name="lastName"
                          className="form-control"
                          placeholder="Nom de l'enseignant"
                          value={formData.lastName}
                          onChange={handleChange}
                          required
                          style={{
                            borderColor: "#D1D5DB",
                            borderRadius: "8px",
                            padding: "10px",
                            backgroundColor: "#FFFFFF",
                            boxShadow: "none",
                            outline: "none",
                            transition: "border-color 0.2s ease-in-out"
                          }}
                          onFocus={(e) => (e.target.style.borderColor = "#2563EB")}
                          onBlur={(e) => (e.target.style.borderColor = "#D1D5DB")}
                        />
                      </div>
                    </div>
                    <div className="col-lg-6 mb-3">
                      <div className="form-group">
                        <label
                          className="text-label"
                          style={{
                            color: "#1D1D1B",
                            fontWeight: "500",
                            marginBottom: "8px",
                            display: "block",
                          }}
                        >
                          Email{" "}
                          <span
                            style={{ color: "#F2BC00", fontWeight: "bold" }}
                          >
                            *
                          </span>
                        </label>
                        <input
                          type="email"
                          name="email"
                          className="form-control"
                          placeholder="<EMAIL>"
                          value={formData.email}
                          onChange={handleChange}
                          required
                          style={{
                            borderColor: "#D1D5DB",
                            borderRadius: "8px",
                            padding: "10px",
                            backgroundColor: "#FFFFFF",
                            boxShadow: "none",
                            outline: "none",
                            transition: "border-color 0.2s ease-in-out"
                          }}
                          onFocus={(e) => (e.target.style.borderColor = "#2563EB")}
                          onBlur={(e) => (e.target.style.borderColor = "#D1D5DB")}
                        />
                      </div>
                    </div>
                    <div className="col-lg-6 mb-3">
                      <div className="form-group">
                        <label
                          className="text-label"
                          style={{
                            color: "#1D1D1B",
                            fontWeight: "500",
                            marginBottom: "8px",
                            display: "block",
                          }}
                        >
                          Numéro de téléphone
                        </label>
                        <input
                          type="tel"
                          name="phoneNumber"
                          className="form-control"
                          placeholder="+216 XX XXX XXX"
                          value={formData.phoneNumber}
                          onChange={handleChange}
                          style={{
                            borderColor: "#D1D5DB",
                            borderRadius: "8px",
                            padding: "10px",
                            backgroundColor: "#FFFFFF",
                            boxShadow: "none",
                            outline: "none",
                            transition: "border-color 0.2s ease-in-out"
                          }}
                          onFocus={(e) => (e.target.style.borderColor = "#2563EB")}
                          onBlur={(e) => (e.target.style.borderColor = "#D1D5DB")}
                        />
                        <small
                          className="text-muted"
                          style={{
                            color: "#B7B7B7",
                            fontStyle: "italic",
                            display: "block",
                            marginTop: "5px",
                          }}
                        >
                          Ce numéro sera utilisé pour envoyer un SMS de
                          bienvenue à l'enseignant. Format recommandé:
                          +216XXXXXXXX
                        </small>
                      </div>
                    </div>
                    <div className="col-lg-6 mb-3">
                      <div className="form-group">
                        <label
                          className="text-label"
                          style={{
                            color: "#1D1D1B",
                            fontWeight: "500",
                            marginBottom: "8px",
                            display: "block",
                          }}
                        >
                          Niveaux d'enseignement{" "}
                          <span
                            style={{ color: "#F2BC00", fontWeight: "bold" }}
                          >
                            *
                          </span>
                        </label>
                        <Select
                          isMulti
                          options={niveaux.map((niveau) => ({
                            value: niveau.id,
                            label: niveau.nom,
                          }))}
                          onChange={(selectedOptions) =>
                            handleSelectChange(selectedOptions, "niveauIds")
                          }
                          placeholder="Sélectionner plusieurs niveaux"
                          styles={{
                            control: (provided) => ({
                              ...provided,
                              width: "100%",
                              borderColor: "#B7B7B7",
                              borderRadius: "6px",
                              padding: "5px",
                              backgroundColor: "white",
                              boxShadow: "none",
                              "&:hover": {
                                borderColor: "#37A7DF",
                              },
                            }),
                            menu: (provided) => ({
                              ...provided,
                              borderRadius: "4px",
                              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                            }),
                            multiValue: (provided) => ({
                              ...provided,
                              backgroundColor: "#EEF9F5",
                              borderRadius: "4px",
                            }),
                            multiValueLabel: (provided) => ({
                              ...provided,
                              color: "#000080",
                              fontWeight: "500",
                            }),
                            multiValueRemove: (provided) => ({
                              ...provided,
                              color: "#37A7DF",
                              "&:hover": {
                                backgroundColor: "#F2BC00",
                                color: "white",
                              },
                            }),
                          }}
                        />
                      </div>
                    </div>

                    <div className="col-lg-6 mb-3">
                      <div className="form-group">
                        <label
                          className="text-label"
                          style={{
                            color: "#1D1D1B",
                            fontWeight: "500",
                            marginBottom: "8px",
                            display: "block",
                          }}
                        >
                          Matières enseignées{" "}
                          <span
                            style={{ color: "#F2BC00", fontWeight: "bold" }}
                          >
                            *
                          </span>
                        </label>
                        <Select
                          isMulti
                          options={matieres.map((matiere) => ({
                            value: matiere.idMatiere,
                            label: matiere.nomMatiere,
                          }))}
                          onChange={(selectedOptions) =>
                            handleSelectChange(selectedOptions, "matiereIds")
                          }
                          placeholder="Sélectionner plusieurs matières"
                          styles={{
                            control: (provided) => ({
                              ...provided,
                              width: "210%",
                              borderColor: "#B7B7B7",
                              borderRadius: "6px",
                              padding: "5px",
                              backgroundColor: "white",
                              boxShadow: "none",
                              "&:hover": {
                                borderColor: "#37A7DF",
                              },
                            }),
                            menu: (provided) => ({
                              ...provided,
                              borderRadius: "4px",
                              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                            }),
                            multiValue: (provided) => ({
                              ...provided,
                              backgroundColor: "#EEF9F5",
                              borderRadius: "4px",
                            }),
                            multiValueLabel: (provided) => ({
                              ...provided,
                              color: "#000080",
                              fontWeight: "500",
                            }),
                            multiValueRemove: (provided) => ({
                              ...provided,
                              color: "#37A7DF",
                              "&:hover": {
                                backgroundColor: "#F2BC00",
                                color: "white",
                              },
                            }),
                          }}
                        />
                      </div>
                    </div>

                    <div className="col-lg-12 mt-4">
                      <button
                        type="submit"
                        className="btn btn-primary"
                        disabled={loading}
                        style={{
                          backgroundColor: "#37A7DF",
                          borderColor: "#37A7DF",
                          padding: "12px 25px",
                          borderRadius: "6px",
                          fontWeight: "500",
                          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                          transition: "all 0.3s ease",
                        }}
                        onMouseOver={(e) =>
                          (e.currentTarget.style.backgroundColor = "#248E39")
                        }
                        onMouseOut={(e) =>
                          (e.currentTarget.style.backgroundColor = "#37A7DF")
                        }
                      >
                        {loading ? (
                          <>
                            <span
                              className="spinner-border spinner-border-sm me-2"
                              role="status"
                              aria-hidden="true"
                            ></span>
                            Création en cours...
                          </>
                        ) : (
                          <>
                            <i className="fa fa-user-plus me-2"></i>
                            Créer l'enseignant
                          </>
                        )}
                      </button>
                      <button
                        type="button"
                        className="btn ms-2"
                        onClick={() => window.history.back()}
                        style={{
                          backgroundColor: "#F6F4EE",
                          borderColor: "#B7B7B7",
                          color: "#1D1D1B",
                          padding: "12px 25px",
                          borderRadius: "6px",
                          fontWeight: "500",
                          transition: "all 0.3s ease",
                        }}
                        onMouseOver={(e) => {
                          e.currentTarget.style.backgroundColor = "#F2BC00";
                          e.currentTarget.style.color = "white";
                          e.currentTarget.style.borderColor = "#F2BC00";
                        }}
                        onMouseOut={(e) => {
                          e.currentTarget.style.backgroundColor = "#F6F4EE";
                          e.currentTarget.style.color = "#1D1D1B";
                          e.currentTarget.style.borderColor = "#B7B7B7";
                        }}
                      >
                        <i className="fa fa-arrow-left me-2"></i>
                        Retour
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
  );
};

export default AjouterEnseignant;
