import React, { useEffect, useState } from 'react';
import { useKeycloak } from '@react-keycloak/web';
import axiosInstance from '../../services/axiosService';
import { useNavigate } from 'react-router-dom';
import { Form } from 'react-bootstrap';

const AbonnementEtudiant = () => {
  const { keycloak } = useKeycloak();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [imageUrls, setImageUrls] = useState({});
  const [userId, setUserId] = useState(null);
  const [successMessage, setSuccessMessage] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [abonnements, setAbonnements] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  // Charger les images
  const loadImage = async (imageId) => {
    try {
      const response = await axiosInstance.get(
        `/api/imageAbonnement/load/${imageId}`,
        {
          responseType: 'blob',
          headers: { Accept: 'image/*' },
        }
      );
      const blobUrl = URL.createObjectURL(new Blob([response.data]));
      setImageUrls((prev) => ({ ...prev, [imageId]: blobUrl }));
    } catch (error) {
      console.error('Error loading image:', error);
      setImageUrls((prev) => ({
        ...prev,
        [imageId]: prev[imageId] || 'placeholder-image.jpg',
      }));
    }
  };

  useEffect(() => {
    return () => {
      Object.values(imageUrls).forEach((url) => URL.revokeObjectURL(url));
    };
  }, [imageUrls]);

  useEffect(() => {
    if (keycloak.authenticated) {
      const fetchEtudiantId = async () => {
        try {
          const { preferred_username: username, email } = keycloak.tokenParsed;

          const response = await axiosInstance.get('/api/etudiants');
          const etudiant = response.data.find(
            (e) => e.username === username || e.email === email
          );

          if (etudiant) {
            setUserId(etudiant.id || etudiant.idEtudiant);
            console.log('Étudiant trouvé :', etudiant);
          } else {
            if (response.data.length > 0) {
              const firstEtudiant = response.data[0];
              setUserId(firstEtudiant.id || firstEtudiant.idEtudiant);
              console.warn('Aucun étudiant ne correspond. Utilisation du premier par défaut.');
            } else {
              setError('Aucun étudiant trouvé dans le système.');
            }
          }
        } catch (error) {
          console.error('Erreur lors de la récupération des étudiants :', error);
          setError('Impossible de récupérer la liste des étudiants.');
        }
      };

      fetchEtudiantId();
    }
  }, [keycloak]);

  useEffect(() => {
    const fetchAbonnements = async () => {
      if (!userId) return;

      try {
        setLoading(true);
        setError(null);

        const response = await axiosInstance.get(`/api/etudiants/${userId}/abonnement`);

        if (Array.isArray(response.data)) {
          setAbonnements(response.data);
          setTotalPages(1);
        } else {
          setAbonnements(response.data.content || []);
          setTotalPages(response.data.totalPages || 1);
        }
      } catch (error) {
        console.error('Erreur lors du chargement des abonnements:', error);
        setError(error.response?.data?.message || 'Erreur lors du chargement des abonnements');
      } finally {
        setLoading(false);
      }
    };

    fetchAbonnements();
  }, [userId, currentPage]);

  const abonnementsFiltres = abonnements.filter((abonnement) =>
    abonnement.nom?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const goToNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1);
  };

  const goToPrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

  return (
    <div className="position-relative">
      <div className="container-fluid px-4" style={{ backgroundColor: "#F6F4EE", minHeight: "100vh" }}>
        <div className="row page-titles mx-0 d-flex align-items-center justify-content-between flex-wrap">
          <div className="col-auto">
            <h4 className="fw-bold" style={{ color: "#000080" }}>
              Tous les Abonnements
            </h4>
          </div>

          <div className="col-md-4">
            <Form.Control
              type="text"
              placeholder="🔍 Rechercher un Abonnement..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="shadow-sm rounded-pill px-3"
              style={{
                backgroundColor: "#EEF9F5",
                borderColor: "#B7B7B7",
                color: "#1D1D1B",
              }}
            />
          </div>

          <div className="col-auto">
            <button
              className="btn rounded-pill shadow-sm px-4"
              style={{
                backgroundColor: "#F2BC00",
                color: "#1D1D1B",
                border: "none",
              }}
              onClick={() => navigate("/abonnements/ajouter")}
            >
              + Ajouter un Abonnement
            </button>
          </div>
        </div>

        {error && <div className="alert alert-danger">{error}</div>}

        <div className="row">
          {abonnementsFiltres.length === 0 ? (
            <p className="text-muted">Aucun abonnement trouvé.</p>
          ) : (
            abonnementsFiltres.map((abonnement) => (
              <div key={abonnement.id} className="col-lg-4 col-md-6 mb-4">
                <div className="card shadow-sm h-100 border-0 hover-shadow">
                  {abonnement.imageAbonnement ? (
                    <img
                      className="card-img-top"
                      src={
                        imageUrls[abonnement.imageAbonnement.idImage] ||
                        "placeholder-image.jpg"
                      }
                      alt={abonnement.nom}
                      style={{
                        height: "250px",
                        objectFit: "cover",
                        borderTopLeftRadius: "0.5rem",
                        borderTopRightRadius: "0.5rem",
                      }}
                      onLoad={() =>
                        !imageUrls[abonnement.imageAbonnement.idImage] &&
                        loadImage(abonnement.imageAbonnement.idImage)
                      }
                      onError={(e) => {
                        if (!e.target.retryAttempted) {
                          e.target.retryAttempted = true;
                          loadImage(abonnement.imageAbonnement.idImage);
                        }
                      }}
                    />
                  ) : (
                    <p className="text-muted text-center">Pas d'image</p>
                  )}
                  <div className="card-body">
                    <h5 className="card-title" style={{ color: "#37A7DF" }}>
                      {abonnement.nom}
                    </h5>
                    <p style={{ color: "#1D1D1B" }}>
                      <strong>Description :</strong> {abonnement.description}
                    </p>
                    <p style={{ color: "#1D1D1B" }}>
                      <strong>Prix :</strong> {abonnement.prix} DT
                    </p>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        <div className="d-flex justify-content-center mt-4">
          <button
            className="btn mx-2"
            style={{
              backgroundColor: "#37A7DF",
              color: "white",
              borderRadius: "20px",
              padding: "6px 18px",
              border: "none",
            }}
            onClick={goToPrevPage}
            disabled={currentPage === 1}
          >
            <i className="fa fa-arrow-left me-1" /> Précédent
          </button>
          <span className="align-self-center text-dark fw-semibold">
            Page <span style={{ color: "#F2BC00" }}>{currentPage}</span> sur{" "}
            <span>{totalPages}</span>
          </span>
          <button
            className="btn mx-2"
            style={{
              backgroundColor: "#37A7DF",
              color: "white",
              borderRadius: "20px",
              padding: "6px 18px",
              border: "none",
            }}
            onClick={goToNextPage}
            disabled={currentPage === totalPages}
          >
            Suivant <i className="fa fa-arrow-right ms-1" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default AbonnementEtudiant;
